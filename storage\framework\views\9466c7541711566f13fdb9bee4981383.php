<!-- Modal -->
<div class="modal fade" id="backDropModalEmail" data-bs-backdrop="static" tabindex="-1">
    <div class="modal-dialog">
        <form class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="backDropModalTitle">Email Signature Edit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="message"></div>
                <div class="row fw-bold">
                    <input type="hidden" id="group-id-template">
                    <div class="col-md-4">
                        <p class="mb-2">Group Name:</p>
                    </div>
                    <div class="col-md-8"><span id="name-in-template"></span></div>
                    
                </div>

                <div class="row g-2 mt-3">
                    <div id="agent-note">
                        <div class="snow-editor-agent-notes"></div>
                    </div>
                    <div class="d-none">
                        <input type="checkbox" class="form-check-input" name="signatureCheckbox" id="signatureCheckbox">
                        <label for="signatureCheckbox"><span style="margin-left: 3px">Active</span></label>
                    </div>
                </div>

                <script>
                    const fullToolbar = [
                        [{ font: [] }, { size: [] }],
                        ['bold', 'italic', 'underline', 'strike'],
                        [{ color: [] }, { background: [] }],
                        [{ script: 'super' }, { script: 'sub' }],
                        [{ header: '1' }, { header: '2' }, 'blockquote', 'code-block'],
                        [{ list: 'ordered' }, { list: 'bullet' }, { indent: '-1' }, { indent: '+1' }],
                        [{ align: [] }],
                        ['direction'],
                        ['link', 'image', 'video'],
                        ['formula'],
                        ['clean']
                    ];

                    let quill; 

                    document.addEventListener("DOMContentLoaded", function() {
                      
                        if (!window.quill) {
                            window.quill = new Quill('.snow-editor-agent-notes', {
                                bounds: '.snow-editor-agent-notes',
                                modules: {
                                    formula: true,
                                    toolbar: fullToolbar
                                },
                                theme: 'snow'
                            });
                        }

                      
                        const groupIdElement = document.getElementById('group-id-template');
                        if (groupIdElement) {
                            const groupId = groupIdElement.value;
                            if (groupId) {
                                getSignatureData(groupId);
                            }
                        }
                        window.quill.on('text-change', function(delta, oldDelta, source) {
            if (source === 'user') {
                const images = document.querySelectorAll('.snow-editor-agent-notes img');
                images.forEach(img => {
                    img.style.width = '178px';
                });
            }
        });

                       
                        $('#save-button-template').click(function() {
                            if (window.quill) {
                                let message = document.getElementById('message');
                                let groupId = document.getElementById('group-id-template').value;
                                let sig = document.getElementById('signatureCheckbox').checked;
                                let editorContent = window.quill.root.innerHTML; 
                                editorContent = editorContent.replace(/<\s*(br|hr)\s*\/?>/gi, '');

                                // Remove empty tags (<div></div>, <span></span>)
                                editorContent = editorContent.replace(/<[^\/>]+>\s*<\/[^>]+>/g, '');
                                editorContent = editorContent.replace(/<img\s+([^>]*?)>/g, function(match, p1) {
                                    return `<img ${p1} width="178">`;
                                });

                                $.ajax({
                                    url: '<?php echo e(route("backend.email.signaturestore")); ?>',
                                    type: 'POST',
                                    data: {
                                        content: editorContent,
                                        groupId: groupId,
                                        status: sig,
                                        _token: '<?php echo e(csrf_token()); ?>'
                                    },
                                    success: function(response) {
                                        let alertDiv = `<div class="alert alert-success mt-2" role="alert">${response.message}</div>`;

                                        if (response.status === 'success') {
                                            message.innerHTML = alertDiv;
                                        } else {
                                            message.innerHTML = `<div class="alert alert-danger mt-2" role="alert">${response.message}</div>`;
                                        }

                                        setTimeout(function() {
                                            message.innerHTML = '';
                                        }, 3000);
                                    },
                                    error: function(xhr, status, error) {
                                        message.innerHTML = `<div class="alert alert-danger mt-2" role="alert">An unexpected error occurred.</div>`;
                                        setTimeout(function() {
                                            message.innerHTML = '';
                                        }, 3000);
                                    }
                                });
                            } else {
                                console.error('Quill editor is not initialized.');
                            }
                        });
                    });

                    function getSignatureData(agentGroupId) {
                        $.ajax({
                            url: '<?php echo e(route("backend.email.signature.find")); ?>',
                            type: 'POST',
                            data: {
                                groupId: agentGroupId,
                                _token: '<?php echo e(csrf_token()); ?>'
                            },
                            success: function(response) {
                                let fileContent = response.fileGet;
                                let fileStatus = response.status;

                                if (!window.quill) {
                                    window.quill = new Quill('.snow-editor-agent-notes', {
                                        bounds: '.snow-editor-agent-notes',
                                        modules: {
                                            formula: true,
                                            toolbar: fullToolbar
                                        },
                                        theme: 'snow'
                                    });
                                }

                                if (window.quill) {
                                    window.quill.clipboard.dangerouslyPasteHTML(fileContent);
                                }

                                document.getElementById('signatureCheckbox').checked = (fileStatus == 1);
                            },
                            error: function(xhr, status, error) {
                                console.error('Error fetching signature data:', status, error);
                            }
                        });
                    }
                </script>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" id="save-button-template" class="btn btn-primary">Save</button>
            </div>
        </form>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\crm-ct\crm-ctt\resources\views/backend/agentgroup/emailsignature.blade.php ENDPATH**/ ?>