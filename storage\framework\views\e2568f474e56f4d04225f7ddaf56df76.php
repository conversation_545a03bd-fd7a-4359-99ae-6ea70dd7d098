<style>
    #edit-Form:hover {
        text-decoration: underline;
      }
    .test-form:hover{
        text-decoration: underline !important;
    }
    .test-form{
        padding-top: 4px;
        cursor:pointer;
        margin-right:34px;
        font-weight: 500;
    }
</style>
<div class="modal fade" id="backDropModalMailConfiguration" data-bs-backdrop="static" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="configGroupName"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            
            <div class="row">
                <div class="col-6 ps-4 d-flex align-items-center">
                    <label class="switch">
                        <input type="checkbox" class="switch-input" id="checkConfig" name="checkEmailConfig" />
                        <span class="switch-toggle-slider">
                            <span class="switch-on">
                                <i class="ti ti-check"></i>
                            </span>
                            <span class="switch-off">
                                <i class="ti ti-x"></i>
                            </span>
                        </span>
                        <span class="switch-label">Custom email config</span>
                    </label>
                </div>
                <div class="col-6 d-flex justify-content-end pe-4">
                    <a class="test-form" data-bs-toggle="modal" data-bs-target="#staticBackdropone"
                        onclick="testEmpty()" >Test</a>
                </div>
            </div>

            
            <div class="defaultenv ms-4 mt-3">
                <p>Mailer Name: <?php echo e(env('MAIL_MAILER')); ?></p>
                <p>Host: <?php echo e(env('MAIL_HOST')); ?></p>
                <p>Port: <?php echo e(env('MAIL_PORT')); ?></p>
                <p>User Name: <?php echo e(env('MAIL_USERNAME')); ?></p>
                <p style="word-wrap: break-word">Password: <?php echo e(env('MAIL_PASSWORD')); ?></p>
                <p>Encryption: <?php echo e(env('MAIL_ENCRYPTION')); ?></p>
                <p>From Address: <?php echo e(env('MAIL_FROM_ADDRESS')); ?></p>
                <p>From Name: <?php echo e(env('MAIL_FROM_NAME')); ?></p>
                <div class="row">
                    <div id="response-message-default"></div>
                </div>
                <form id="defaultForm">
                    <input type="hidden" name="checkEmailConfig" value="0">
                    <input type="hidden" name="pos_type" value="b2b">
                    <input type="hidden" id="mail-group-id-default" name="mail_group_id">
                    <div class="modal-footer">
                        <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary">Save</button>
                    </div>
                </form>
            </div>

            
            <div class="mainContainer">
                <form id="mailConfigurationForm">
                    <input type="hidden" name="pos_type" value="b2b">
                    <input type="hidden" name="checkEmailConfig" value="1">
                    <input type="hidden" id="mail-group-id" name="mail_group_id">
                    <div>
                        <div class="d-flex justify-content-end" onclick="showForm()" 
                            style="padding-top: 4px;cursor:pointer;margin-right:45px;">
                            <b id="edit-Form">Edit</b>
                        </div>
                        
                        <div class="container mt-3" id="text-static-container">
                            <div class="row">
                                <div class="col-6">
                                    <label for="mail_mailer_text" class="form-label"><b>Mailer Name:</b> </label>
                                    <span id="mail_mailer_text"></span>
                                </div>
                                <div class="col-6">
                                    <label for="mail_host_text" class="form-label"><b>Host:</b> </label>
                                    <span id="mail_host_text"></span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <label for="mail_port_text" class="form-label"><b>Port: </b></label>
                                    <span id="mail_port_text"></span>
                                </div>
                                <div class="col-6">
                                    <label for="mail_user_name_text" class="form-label"><b>User Name:</b> </label>
                                    <span id="mail_user_name_text"></span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <label for="mail_password_text" class="form-label"><b>Password:</b> </label>
                                    <span id="mail_password_text"></span>
                                </div>
                                <div class="col-6">
                                    <label for="mail_encryption_text" class="form-label"><b>Encryption: </b></label>
                                    <span id="mail_encryption_text"></span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <label for="mail_from_address_text" class="form-label"><b>From Address:</b> </label>
                                    <span id="mail_from_address_text"></span>
                                </div>
                                <div class="col-6">
                                    <label for="mail_from_name_text" class="form-label"><b>From Name:</b> </label>
                                    <span id="mail_from_name_text"></span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <label for="mail_bcc_address_text" class="form-label"><b>BCC Address:</b> </label>
                                    <span id="mail_bcc_address_text" style="word-wrap: break-word"></span>
                                </div>
                                <div class="col-6">
                                    <label for="mail_cc_address_text" class="form-label"><b>CC Address:</b> </label>
                                    <span id="mail_cc_address_text" style="word-wrap: break-word"></span>
                                </div>
                            </div>
                        </div>

                        
                        <div class="modal-body" id="mailConfigForm" style="display: none">
                            <div class="row">
                                <div class="col-6">
                                    <label for="mail_mailer" class="form-label">Mailer Name</label>
                                    <input type="text" id="mail_mailer" class="form-control" placeholder=""
                                        name="mail_mailer">
                                </div>
                                <div class="col-6">
                                    <label for="mail_host" class="form-label">Host</label>
                                    <input type="text" id="mail_host" class="form-control" placeholder=""
                                        name="mail_host">
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-6">
                                    <label for="mail_port" class="form-label">Port</label>
                                    <input type="text" id="mail_port" class="form-control" placeholder=""
                                        name="mail_port">
                                </div>
                                <div class="col-6">
                                    <label for="mail_user_name" class="form-label">User Name</label>
                                    <input type="text" id="mail_user_name" class="form-control" placeholder=""
                                        name="mail_user_name">
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-6">
                                    <label for="mail_password" class="form-label">Password</label>
                                    <input type="password" id="mail_password" class="form-control" placeholder=""
                                        name="mail_password">
                                </div>
                                <div class="col-6">
                                    <label for="mail_encryption" class="form-label">Encryption</label>
                                    <select class="form-select" id="mail_encryption" name="mail_encryption">
                                        <option value="" selected>Select</option>
                                        <?php $__currentLoopData = config('custom-app.mailer_encryption'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($value); ?>"><?php echo e($key); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-6">
                                    <label for="mail_from_address" class="form-label">From Address</label>
                                    <input type="text" id="mail_from_address" class="form-control" placeholder=""
                                        name="mail_from_address">
                                </div>
                                <div class="col-6">
                                    <label for="mail_from_name" class="form-label">From Name</label>
                                    <input type="text" id="mail_from_name" class="form-control" placeholder=""
                                        name="mail_from_name">
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <label for="mail_bcc_address" class="form-label">BCC Address <i>( Separated by ; )</i></label>
                                    <textarea name="mail_bcc_address" cols="30" rows="1" id="mail_bcc_address" class="form-control"></textarea>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <label for="mail_cc_address" class="form-label">CC Address <i>( Separated by ; )</i></label>
                                    <textarea name="mail_cc_address" id="mail_cc_address" cols="30" rows="1" class="form-control"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div id="response-message"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary">Save</button>
                    </div>

                </form>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="staticBackdropone" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="modalScrollableTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalScrollableTitle"></h5>
                <div type="button" data-bs-dismiss="modal" class="btn-close" aria-label="Close"
                    style="z-index:999;padding:0px;margin-right:6px;"></div>

            </div>
            <div class="modal-body">
                <form id="mailConfigurationTest">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="mail_group_id" id="mail-group-id-test">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="mail_to" class="form-label">To</label>
                            <input type="text" class="form-control" id="mail_to" name="mail_to"
                                placeholder="To" maxlength="40" required>
                        </div>
                        <div class="col-md-6">
                            <label for="mail_cc" class="form-label">CC <i>( Separated by ; )</i></label>
                            <input type="text" class="form-control" id="mail_cc" name="mail_cc"
                                placeholder="CC">
                        </div>
                        <div class="col-md-6 mt-2">
                            <label for="mail_bcc" class="form-label">BCC <i>( Separated by ; )</i></label>
                            <input type="text" class="form-control" id="mail_bcc" name="mail_bcc"
                                placeholder="BCC">
                        </div>
                        <div class="col-md-6 mt-2">
                            <label for="mail_subject" class="form-label">Subject</label>
                            <input type="text" class="form-control" id="mail_subject" name="mail_subject"
                                placeholder="Subject" required>
                        </div>
                        <div class="col-md-12 mt-2">
                            <label for="mail_text" class="form-label">Text</label>
                            <textarea name="mail_text" id="mail_text" cols="30" rows="3" class="form-control"
                                placeholder="Text..." required></textarea>
                        </div>
                        <div class="row mt-2">
                            <div id="loader" style="display:none;" class="spinner-border" role="status"></div>
                            <div id="response-message-test"></div>
                        </div>
                        <div class="col-12 d-flex justify-content-end">
                            <div class="btn btn-secondary" data-bs-dismiss="modal">Close</div>
                            <button type="submit" class="btn btn-primary">Send</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
  // Main Form
    $(document).ready(function() {
        var csrfToken = $('meta[name="csrf-token"]').attr('content');

        $('#mailConfigurationForm').on('submit', function(e) {
            e.preventDefault();

            $.ajax({
                url: '<?php echo e(route('backend.mailConfigurationForm')); ?>',
                type: 'POST',
                data: $(this).serialize(),
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                },
                success: function(response) {
                    if (response.success) {
                        $('#response-message').html(
                            '<div  class="alert alert-success" ><p>' + response
                            .message + '</p></div>');
                    } else {
                        $('#response-message').html(
                            '<div  class="alert alert-danger" ><p>' + response.message +
                            '</p></div>');
                    }

                    setTimeout(function() {
                        $('#response-message').html('');
                    }, 3000);

                },
                error: function(xhr) {
                    $('#response-message').html(
                        '<div  class="alert alert-danger" ><p>Error: ' + xhr
                        .responseJSON.message + '</p></div>');
                    setTimeout(function() {
                        $('#response-message').html('');
                    }, 3000);
                },
            });
        });
    });

    // Default Form 
    $(document).ready(function() {
        var csrfToken = $('meta[name="csrf-token"]').attr('content');

        $('#defaultForm').on('submit', function(e) {
            e.preventDefault();

            $.ajax({
                url: '<?php echo e(route('backend.defaultForm')); ?>',
                type: 'POST',
                data: $(this).serialize(),
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                },
                beforeSend: function() {
                    $('#loader').show();
                },
                success: function(response) {
                    if (response.success) {
                        $('#response-message-default').html(
                            '<div  class="alert alert-success" ><p>' + response
                            .message + '</p></div>');
                    } else {
                        $('#response-message-default').html(
                            '<div  class="alert alert-danger" ><p>' + response.message +
                            '</p></div>');
                    }

                    setTimeout(function() {
                        $('#response-message-default').html('');
                    }, 3000);

                },
                error: function(xhr) {
                    $('#response-message-default').html(
                        '<div  class="alert alert-danger" ><p>Error: ' + xhr
                        .responseJSON.message + '</p></div>');
                    setTimeout(function() {
                        $('#response-message-default').html('');
                    }, 3000);
                },
                complete: function() {
                    $('#loader').hide();
                }
            });
        });
    });

    // Test Form
    $(document).ready(function() {
        var csrfToken = $('meta[name="csrf-token"]').attr('content');

        $('#mailConfigurationTest').on('submit', function(e) {
            e.preventDefault();

            $.ajax({
                url: '<?php echo e(route('backend.mailConfigurationTest')); ?>',
                type: 'POST',
                data: $(this).serialize(),
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                },
                beforeSend: function() {
                    $('#loader').show();
                },
                success: function(response) {
                    if (response.success) {
                        $('#response-message-test').html(
                            '<div  class="alert alert-success" ><p>' + response
                            .message + '</p></div>');
                    } else {
                        $('#response-message-test').html(
                            '<div  class="alert alert-danger" ><p>' + response.message +
                            '</p></div>');
                    }

                    setTimeout(function() {
                        $('#response-message-test').html('');
                    }, 3000);

                },
                error: function(xhr) {
                    $('#response-message-test').html(
                        '<div  class="alert alert-danger" ><p>Error: ' + xhr
                        .responseJSON.message + '</p></div>');
                    setTimeout(function() {
                        $('#response-message-test').html('');
                    }, 3000);
                },
                complete: function() {
                    $('#loader').hide();
                }
            });
        });
    });

    $(document).ready(function() {
        $('.mainContainer').hide();


        $('#checkConfig').click(function() {
            $('.mainContainer').toggle();

            if ($('.mainContainer').is(':visible')) {
                $('.defaultenv').hide();
            } else {
                $('.defaultenv').show();
            }
        })
    })

    function showForm() {
        document.querySelector('#mailConfigForm').style.display = "block";
    }

    function testEmpty() {
        document.getElementById('mail_to').value = ''
        document.getElementById('mail_cc').value = ''
        document.getElementById('mail_bcc').value = ''
        document.getElementById('mail_subject').value = ''
        document.getElementById('mail_text').value = ''
    }
</script>
<?php /**PATH C:\Users\<USER>\Desktop\crm-ct\crm-ctt\resources\views/backend/agentgroup/mailConfigurationForm.blade.php ENDPATH**/ ?>