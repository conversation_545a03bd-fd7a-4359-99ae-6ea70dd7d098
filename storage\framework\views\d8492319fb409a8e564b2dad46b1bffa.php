
<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasEndDeposit" aria-labelledby="offcanvasEndLabel"
    style="width:50%;">
    <div class="offcanvas-header">
        <h5 id="offcanvasEndLabel" class="offcanvas-title agency_account_name"></h5>
        <div class="">
            <button class="btn btn-primary pr-3" data-bs-toggle="modal" data-bs-target="#transactionModal"
                id="showTransactionsBtn">
                All Transaction
            </button>

            <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
          
        </div>
    </div>

    <div class="offcanvas-body mx-0 flex-grow-0">
        <!-- Success Message Alert (Initially Hidden) -->
        <div class="deposit-form">
            <input type="hidden" id="hiddenDataId" name="agency_id">
            <form action="<?php echo e(route('backend.agencygroup.deposit')); ?>" method="POST" id="deposit">
                <div id="errorMessage"></div>
                <div id="success-alert" class="alert alert-success success-alert d-none " role="alert"></div>
                <?php echo csrf_field(); ?>

                <div class="row">
                    <div class="col-4">
                        <div class="mb-3">
                            <label for="trans_mode" class="form-label">Transaction</label>
                            <select class="form-select" id="trans_mode" name="trans_mode">
                                <?php $__currentLoopData = config('custom-app.Transaction_mode'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($key); ?>"><?php echo e($value); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <span class="text-danger error-text trans_mode_error"></span>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="mb-3 mt-4">
                            <div class="input-group input-group-sm" style="margin-top: 0px;display: flex;width: 100%; ">
                                <input type="text" class="input-group-text"
                                    style="border-top-right-radius: 0;border-bottom-right-radius: 0;width:21%;padding:8px;"
                                    id="deposit_currency" name="deposit_currency" readonly value="CAD"></input>
                                <input type="text" class="form-control numeral-mask edit-credit-card-mask"
                                    id="deposit_amount" name="deposit_amount"
                                    style="border-top-left-radius: 0; border-bottom-left-radius: 0;" maxlength="6">
                            </div>
                            <span class="text-danger error-text deposit_amount_error"></span>
                        </div>
                    </div>
                    <div class="col-4" id="transaction_type_container">
                        <div class="mb-3">
                            <label for="trans_type" class="form-label">Transaction Type</label>
                            <select class="form-select" id="trans_type" name="trans_type">
                                <?php $__currentLoopData = config('custom-app.Transaction_Type'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($key); ?>"><?php echo e($value); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <span class="text-danger error-text trans_type_error"></span>
                        </div>
                    </div>
                    <div class="col-4" id="depit_type_container">
                        <div class="mb-3">
                            <label for="depit_type" class="form-label">Debit Type</label>
                            <select class="form-select" id="depit_type" name="depit_type">
                                <option value="" selected disabled>Select</option>
                                <?php $__currentLoopData = config('custom-app.Debit_Type'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($key); ?>"><?php echo e($value); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <span class="text-danger error-text depit_type_error"></span>
                        </div>
                    </div>
                    <div class="col-8">
                        <div class="mb-3">
                            <label for="remarks" class="form-label">Remarks</label>
                            <textarea name="remarks" id="remarks" class="form-control"></textarea>
                            <span class="text-danger error-text remarks_error"></span>
                        </div>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">Submit</button>
                
                <div class="col-6 spinner-border" role="status" id="deposit-Loader" style=" display:none">

                </div>
            </form>
        </div>
        <div class="deposit-percent mt-3">
            <form action="<?php echo e(route('backend.agencygroup.formdata.store')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div id="success-alert" class="alert alert-success success-alert d-none" role="alert"></div>

                <div class="row align-items-center">
                    <div class="col-4">
                        <label>Total Deposit:</label>
                        <span class="fw-bold" id="total_Deposit_amount">0.00</span>
                    </div>

                    <div class="col-4">
                        <label for="allowed_credit_deposit_percent" class="form-label">Credit Allowed From
                            Deposit</label>
                        <div class="mb-1 input-group">
                            <input type="text" name="allowed_credit_deposit_percent"
                                id="allowed_credit_deposit_percent" class="form-control">
                            <span class="input-group-text"
                                style="border-top-right-radius: 0; border-bottom-right-radius: 0; width: 21%; padding: 8px;"
                                id="deposit_currency" name="deposit_currency" readonly>%</span>
                            <span class="text-danger error-text allowed_credit_deposit_percent_error"></span>
                        </div>
                    </div>
                    <div class="col-4">
                        <button type="submit" class="btn btn-primary mt-3">Submit</button>
                        <div class="spinner-border ms-2" role="status" id="deposit-percent-Loader"
                            style="display: none;"></div>
                    </div>
                </div>

                <div class="row mt-2">
                    <div class="col-6">
                        <label>Credit From Deposit:</label>
                        <span class="fw-bold" id="allowed_Deposit_amount">0.00</span>
                    </div>

                    <div class="col-2">
                    </div>
                </div>
            </form>
        </div>

        <div class="deposit-limit mt-2">
            <form action="<?php echo e(route('backend.agencygroup.formdata.store')); ?>" method="POST">
                <div id="success-alert" class="alert alert-success success-alert d-none " role="alert"></div>
                <?php echo csrf_field(); ?>
                <fieldset class="p-3 border rounded allowed-credit-limit mb-3 mt-3">
                    <div class="row mt-2">
                        <div class="col-6">
                            <div class="mb-1 input-group" style="margin-top: 0px;display: flex;width: 100%; ">
                                <input type="text" class="input-group-text"
                                    style="border-top-right-radius: 0;border-bottom-right-radius: 0;width:21%;padding:8px;"
                                    id="deposit_currency" name="deposit_currency" readonly value="CAD"></input>
                                <input type="text" name="allowed_credit_limit" id="allowed_credit_limit"
                                    class="form-control"
                                    style="border-top-left-radius: 0; border-bottom-left-radius: 0;">
                                <span class="text-danger error-text allowed_credit_limit_error"></span>
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">Submit</button>
                    <div class="col-6 spinner-border" role="status" id="deposit-percent-Loader"
                        style=" display:none">
                    </div>
                </fieldset>
            </form>
        </div>
        <div class="mb-2">Total Credit Limit: <span class="fw-bold" id="total_credit_limit">0.00</span></div>
        
        <div class="mb-2">Pending Due: <span class="fw-bold" id="pending_due">0.00</span></div>
        <div class="mb-2">Cash Balance: <span class="fw-bold" id="cash_balance">0.00</span></div>
        <div class="mb-2">Usable Credit Limit: <span class="fw-bold" id="usable_credit_limit">0.00</span></div>
        <div class="temporary-credit mt-2">
            <form action="<?php echo e(route('backend.agencygroup.formdata.store')); ?>" method="POST">
                <div id="success-alert" class="alert alert-success success-alert d-none " role="alert"></div>
                <?php echo csrf_field(); ?>
                <fieldset class="p-3 border rounded temporary-credits mb-3 mt-3">
                    <div class="row mt-2">
                        <div class="col-6">
                            <div class="mb-2 input-group" style="margin-top: 0px;display: flex;width: 100%; ">
                                <input type="text" class="input-group-text"
                                    style="border-top-right-radius: 0;border-bottom-right-radius: 0;width:21%;padding:8px;"
                                    id="deposit_currency" name="deposit_currency" readonly value="CAD"></input>
                                <input type="text" name="temporary_credit" id="temporary_credit"
                                    class="form-control">
                                <span class="text-danger error-text temporary_credit_error"></span>
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">Submit</button>
                    <div class="col-6 spinner-border" role="status" id="deposit-percent-Loader"
                        style=" display:none">
                    </div>
                </fieldset>
            </form>
        </div>
        <div class="credit-rule mt-4">
            <form action="<?php echo e(route('backend.agencygroup.formdata.store')); ?>" method="POST">
                <div id="success-alert" class="alert alert-success success-alert d-none " role="alert"></div>
                <?php echo csrf_field(); ?>
                <fieldset class="p-3 border rounded credit-rules mb-3 mt-3">
                    <div class="row mt-2">
                        <div class="col-6">
                            <div class="mb-2">
                                <select class="form-select" id="credit_rule" name="credit_rule">
                                    <?php $__currentLoopData = config('custom-app.Credit_rule'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($key); ?>">
                                            <?php echo e($value); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <span class="text-danger error-text credit_rule_error"></span>
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">Submit</button>
                    <div class="col-6 spinner-border" role="status" id="deposit-percent-Loader"
                        style=" display:none">
                    </div>
                </fieldset>
            </form>
        </div>
    </div>
</div>

<div class="modal fade" id="transactionModal" tabindex="-1" aria-hidden="true">

    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title agency_account_name" id="transactionModalLabel">All Transactions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                
            </div>
            <div class="modal-body">
                <table id="transactionTable" class="table table-bordered" style="padding: 0px">
                    <thead>
                        <tr>
                            <th>Transaction</th>
                            <th style="width: 145.889px;padding: 9px;">Transaction Type</th>
                            <th>Amount</th>
                            
                            <th style="width: 121.889px;">Remark</th>
                            <th style="width: 95px;">Date</th>
                        </tr>
                    </thead>
                    <tbody class="text-capitalize"></tbody>
                </table>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<script>
    document.addEventListener('change', function() {
        let agency_pos_val = document.getElementById("group_pos_id_edit").value;
        document.getElementById("edit_pos_cl").value = "CAD";
    })
    document.addEventListener("DOMContentLoaded", function() {
        let transModeSelect = document.getElementById("trans_mode");
        let debitTypeContainer = document.getElementById("depit_type_container");
        let TransactionTypeContainer = document.getElementById("transaction_type_container");
        let debitType = document.getElementById("depit_type");

        if (!transModeSelect || !debitTypeContainer || !debitType) {
            console.error("Element not found! Check your HTML IDs.");
            return;
        }

        debitTypeContainer.style.display = "none";
        TransactionTypeContainer.style.display = "block";

        transModeSelect.addEventListener("change", function() {
            let transMode = this.value;

            if (transMode === "debit") {
                debitTypeContainer.style.display = "block";
                TransactionTypeContainer.style.display = "none";
                debitType.value = "";
            } else if (transMode === "credit") {
                debitTypeContainer.style.display = "none";
                TransactionTypeContainer.style.display = "block";
                debitType.value = "";
            } else {
                debitTypeContainer.style.display = "none";
            }
        });
    });
</script>
<script>
    function depositAgent(data) {
        console.log("depositAgent called with data:", data);
        document.getElementById('hiddenDataId').value = data;
        setTimeout(() => depositCalculation(data), 50);
    }

    function depositCalculation(id) {
        $.ajax({
            url: "/backend/deposit/calculation/" + id,
            type: 'POST',
            data: {
                _token: '<?php echo e(csrf_token()); ?>',
            },
            beforeSend: function() {
                console.log("Sending request...");
            },
            success: function(response) {
                // console.log("Response received:", response.agencyGroupInfo.credit_rule);
                $('#total_Deposit_amount').text(response.depositCurrency + " " + (response
                    .total_Deposit_amount ?? 0.00));
                $('#allowed_credit_deposit_percent').val(response.agencyGroupInfo
                    .allowed_credit_deposit_percent);
                $('#allowed_Deposit_amount').text(response.depositCurrency + " " + (response
                    .allowed_credit_deposit_percent_amount ?? 0.00));
                $('#allowed_credit_limit').val(response.agencyGroupInfo.allowed_credit_limit);
                $('#temporary_credit').val(response.agencyGroupInfo.temporary_credit);
                $('#total_credit_limit').text(response.depositCurrency + " " + (response.totalCreditLimit ??
                    0.00));
                $('#total_booking_amount').text(response.totalBookingAmount);
                $('.agency_account_name').text(response.agencyGroupInfo.group_name + " Agency Account ");
                $('#cash_balance').text(response.currentDueAmount ?? 0.00);
                $('#pending_due').text(response.pendingDue);
                $('#usable_credit_limit').text(response.usableCreditLimit);
                let creditRules = response.agencyGroupInfo.credit_rule;
                $('#credit_rule').val(creditRules).trigger('change');
            },
            error: function(xhr, status, error) {
                console.error("Error:", error);
            },
            complete: function() {
                console.log("Request completed.");
            }
        });
    }
</script>
<script>
    $(document).ready(function() {
        // Attach a submit handler to all forms inside the deposit container
        $('.deposit-form, .deposit-percent, .deposit-limit, .temporary-credit, .credit-rule').on('submit',
            'form',
            function(e) {
                e.preventDefault();

                let id = $('#hiddenDataId').val();
                let form = $(this);
                let formData = form.serialize();
                formData += `&group_id=${encodeURIComponent(id)}`;
                let loader = form.find('.spinner-border');
                let successAlert = form.find('.success-alert');

                $.ajax({
                    url: form.attr('action'),
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    beforeSend: function() {
                        successAlert.addClass('d-none').text(''); // Hide success message
                        loader.show();
                        form.find('.error-text').text(''); // Clear previous errors
                    },
                    success: function(response) {
                        if (response.success) {
                            form[0].reset(); // Reset only the submitted form
                            successAlert.removeClass('d-none').text(response.message);

                        }else{
                           
                            
                        }
                    },
                    error: function(xhr) {
                        let errors = xhr.responseJSON.errors;
                        $.each(errors, function(field, messages) {
                            form.find('.' + field + '_error').text(messages[0]);
                        });
                        if(xhr.responseJSON.errorMessage){
                            $('#errorMessage').html(`<div class="alert alert-danger" role="alert">
                            ${xhr.responseJSON.errorMessage}
                        </div>`);
                        setTimeout(function() {
                            $('#errorMessage').html('');
                        }, 3000);

                    }

                    },
                    complete: function() {
                        loader.hide();
                        setTimeout(() => depositCalculation(id), 50);
                        setTimeout(function() {
                            successAlert.addClass(
                                'd-none'); // Hide alert after 3 seconds
                        }, 3000);
                    }
                });
            });
    });
</script>
<script>
    $(document).ready(function() {
        $('#showTransactionsBtn').on('click', function() {
            let id = $('#hiddenDataId').val();

            if ($.fn.DataTable.isDataTable('#transactionTable')) {
                $('#transactionTable').DataTable().clear().destroy();
            }

            $.ajax({
                url: "/backend/all/transaction/" + id,
                type: "GET",
                dataType: "json",
                beforeSend: function() {
                    $('#transactionTable tbody').html(
                        '<tr><td colspan="6">Loading transactions...</td></tr>');
                },
                success: function(response) {
                    if (response.length > 0) {
                        $('#transactionTable').DataTable({
                            data: response,
                            responsive: true,
                            ordering: false,
                            paging: true,
                            searching: true,
                            destroy: true,
                            columns: [{
                                    data: "trans_mode",
                                    defaultContent: "-"
                                },
                                {
                                    data: function(row) {
                                        return row.trans_type || row
                                            .debit_type || "-";
                                    },
                                    defaultContent: "-"
                                },
                                {
                                    data: function(row) {
                                        return "CAD " + row.deposit_amount;
                                    },
                                    defaultContent: "-"
                                },
                                {
                                    data: "remarks",
                                    defaultContent: "-"
                                },
                                {
                                    data: "formatted_date",
                                    defaultContent: "-"
                                }
                            ]
                        });
                    } else {
                        $('#transactionTable tbody').html(
                            '<tr><td colspan="6">No transactions found.</td></tr>');
                    }
                },
                error: function(xhr, status, error) {
                    $('#transactionTable tbody').html(
                        '<tr><td colspan="6" class="text-danger">Error loading transactions.</td></tr>'
                    );
                    console.error("AJAX Error:", error);
                }
            });
        });
    });
</script>
<?php /**PATH C:\Users\<USER>\Desktop\crm-ct\crm-ctt\resources\views/backend/agencygroup/creditLimit.blade.php ENDPATH**/ ?>