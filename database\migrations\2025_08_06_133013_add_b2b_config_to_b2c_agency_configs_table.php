<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('b2c_agency_configs', function (Blueprint $table) {
            $table->text('b2b_config')->after('meta_config')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('b2c_agency_configs', function (Blueprint $table) {
            $table->dropColumn('b2b_config');
        });
    }
};
