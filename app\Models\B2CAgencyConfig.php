<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class B2CAgencyConfig extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'b2c_agency_configs';

    protected $fillable = [
        'agency_profile_id',
        'domain',
        'site_name',
        'platform_name',
        'site_email',
        'market_type',
        'site_phone',
        'portal_id',
        'rsource',
        'content_sources',
        'landing_url',
        'booking_mode',
        'active',
    ];

    protected $casts = [
        'api_key' => 'array',
        'client_id' => 'array',
        'booking_mode' => 'boolean',
        'active' => 'boolean',
    ];

    public function agencyProfile()
    {
        return $this->belongsTo(AgencyProfile::class);
    }
}
