<?php $__env->startSection('title', 'Agent-Group'); ?>
<?php $__env->startSection('vendor-style'); ?>
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.css')); ?>" />
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/sweetalert2/sweetalert2.css')); ?>" />
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/quill/katex.css')); ?>" />
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/quill/editor.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('vendor-script'); ?>
<script src="<?php echo e(asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js')); ?>"></script>
<!-- Flat Picker -->
<script src="<?php echo e(asset('assets/vendor/libs/moment/moment.js')); ?>"></script>
<script src="<?php echo e(asset('assets/vendor/libs/cleavejs/cleave.js')); ?>"></script>
<script src="<?php echo e(asset('assets/vendor/libs/cleavejs/cleave-phone.js')); ?>"></script>
<script src="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.js')); ?>"></script>
<script src="<?php echo e(asset('assets/vendor/js/dropdown-hover.js')); ?>"></script>
<script src="<?php echo e(asset('assets/vendor/libs/sweetalert2/sweetalert2.js')); ?>"></script>
<script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>
<script src="<?php echo e(asset('assets/vendor/libs/cleavejs/cleave.js')); ?>"></script>
<script src="<?php echo e(asset('assets/vendor/libs/cleavejs/cleave-phone.js')); ?>"></script>
<script src="<?php echo e(asset('assets/vendor/libs/quill/katex.js')); ?>"></script>
<script src="<?php echo e(asset('assets/vendor/libs/quill/quill.js')); ?>"></script>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('page-script'); ?>
<script src="<?php echo e(asset('assets/js/agentgroup/index.js')); ?>"></script>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
<style>
   .planname{
    text-decoration: underline !important;
  }
  .planname:hover{
    font-weight: 900 !important;
  }
   .agencycount{
    text-decoration: underline !important;
  }
  .agencycount:hover{
    font-weight: 900 !important;
  }
  .commission-split {
    position: relative;
    top: 8px;
  }

  .commission-split::before {
    position: absolute;
    content: "Commission Sharing and Incentives";
    top: -12px;
    background-color: white;
    padding: 2px;
  }

  .commission-split {
    position: relative;
    top: 8px;
  }

  .commission-split::before {
    position: absolute;
    content: "Commission Sharing and Incentives";
    top: -12px;
    background-color: white;
    padding: 2px;
  }

  .agent-split {
    position: relative;
    top: 8px;
  }

  .agent-split::before {
    position: absolute;
    content: "Agency Contact";
    top: -12px;
    background-color: white;
    padding: 2px;
  }
</style>
<?php if(session('success')): ?>
<div class="container mt-4">
  <div id="successMessage" class="alert alert-success">
    <?php echo e(session('success')); ?>

  </div>
</div>
<?php endif; ?>




<?php if($errors->any()): ?>
<div class="container mt-4" id="error">
  <div class="alert alert-danger">
    <ul style="list-style-type:none">
      <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
      <li><?php echo e($error); ?></li>
      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ul>
  </div>
</div>
<?php endif; ?>

<script>
  $(document).ready(function() {
    setTimeout(function() {
      $('#error').fadeOut(3000);
    }, 3000);
  });
</script>

<div class="card header">
  <div class="card-body py-2">
    <div class="col-md-4 d-flex ">
      <h5 class="mx-2 mt-2 text-white">Agent Groups</h5>
      <div class="mx-2 mt-1">
        <button class="btn btn-primary" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasEnd" aria-controls="offcanvasEnd">Create</button>
      </div>
    </div>
  </div>
</div>
<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasEnd" aria-labelledby="offcanvasEndLabel" style="width: 50%">
  <div class="offcanvas-header">
    <h5 id="offcanvasEndLabel" class="offcanvas-title">Create Agent Group</h5>
    <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
  </div>
  <div class="offcanvas-body mx-0 ">
    <form action="<?php echo e(route('backend.agentgroup.store')); ?>" method="POST">
      <?php echo csrf_field(); ?>
      <div class="row">
        <div class="col-6">
          <div class="input-group input-group-sm mb-3">
            <label for="group_name" class="form-label">Group Name</label>
            <div class=" input-group">
              <input type="text" class="form-control" id="group_name" name="group_name" placeholder="Group Name" required>
            </div>
          </div>
        </div>
        <div class="col-6">
          <div class="mb-3 input-group input-group-sm">
            <label for="group_id" class="form-label">Group Id</label>
            
            <div class=" input-group">
              <input type="text" class="form-control" id="agent_group_id" name="agent_group_id" required maxlength="10" placeholder="Group ID">
              
            </div>
          </div>
        </div>
        <div class="col-6">
          <div class="mb-3">
            <label for="plan_name" class="form-label">Commission Plan</label>
            <select name="plan_name" id="plan_name" class="form-select">
              <option value="" selected>Select a plan</option>
                 <?php $__currentLoopData = $groupcommissionplan; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      <option value="<?php echo e($item->group_commission_plan); ?>"><?php echo e($item->plan_name); ?></option>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          </select>
          </div>
        </div>
      </div>
      <div id="agent_group_id-error-message" class="text-danger text-nowrap">
      </div>
      <div id="agent_group_id-spinner" class="spinner-border text-primary" style="width: 16px; height: 16px; display: none;" role="status">
        <span class="sr-only">Loading...</span>
      </div>
      <fieldset class="p-3 border rounded commission-split d-none">
        <label for="international_sharing_perc" class="form-label">International</label>

        <div class="row">
          <div class="col-md-5">
            <div class="input-group input-group-sm">

              <div class="input-group">
                <input type="text" class="form-control numeral-mask" id="international_sharing_perc" name="international_sharing_perc" value="0" required maxlength="3">
                <span class="input-group-text"> % </span>
              </div>
            </div>
          </div>
          <div class="col-md-1 d-flex align-items-center justify-content-center">
            <i class="ti ti-plus" style="font-size: 2.25rem;color:#17224e !important;font-size: xx-large;"></i>
          </div>
          <div class="col-md-6">
            <div class="input-group input-group-sm">
              <div class="input-group">
                <span class="input-group-text">CAD</span>
                <input type="text" class="form-control numeral-mask" id="international_sharing" name="international_sharing" value="0" required maxlength="3">
              </div>
            </div>
          </div>
        </div>
        <label for="domestic_sharing" class="form-label">Domestic Sharing</label>
        <div class="row">
          <div class="col-md-5">
            <div class="input-group input-group-sm">
              <div class=" input-group">
                <input type="text" class="form-control numeral-mask" id="domestic_sharing_perc" name="domestic_sharing_perc" value="0" required maxlength="3">
                <span class="input-group-text"> % </span>
              </div>
            </div>
          </div>
          <div class="col-md-1 d-flex align-items-center justify-content-center">
            <i class="ti ti-plus" style="font-size: 2.25rem;color:#17224e !important;font-size: xx-large;"></i>
          </div>
          <div class="col-md-6">
            <div class="input-group input-group-sm">

              <div class=" input-group">
                <span class="input-group-text">CAD</span>
                <input type="text" class="form-control numeral-mask" id="domestic_sharing" name="domestic_sharing" value="0" required maxlength="3">
              </div>
            </div>
          </div>
        </div>

        <label for="transporter_sharing" class="form-label">Transborder</label>
        <div class="row">
          <div class="col-md-5">
            <div class="input-group input-group-sm">

              <div class=" input-group">
                <input type="text" class="form-control numeral-mask" id="transporter_sharing_perc" name="transporter_sharing_perc" value="0" required maxlength="3">
                <span class="input-group-text"> % </span>
              </div>

            </div>
          </div>
          <div class="col-md-1 d-flex align-items-center justify-content-center">
            <i class="ti ti-plus" style="font-size: 2.25rem;color:#17224e !important;font-size: xx-large;"></i>
          </div>
          <div class="col-md-6">
            <div class="input-group input-group-sm">

              <div class=" input-group">
                <span class="input-group-text">CAD</span>
                <input type="text" class="form-control numeral-mask" id="transporter_sharing" name="transporter_sharing" value="0" required maxlength="3">

              </div>
            </div>
          </div>
        </div>
      </fieldset>
      <div class=" mt-4 input-group input-group-sm">
        
        <div class=" input-group">
          <input type="hidden" class="form-control" id="agency-pos" name="agency_pos" value="CA" readonly>
        </div>
      </div>
      
      <div class="mb-3">
        
      </div>
      
<div class="row">
  <input type="hidden" class="switch-input booking-show-agent" name="agency_group" value="0"/>
  
  <div class="col-2">
    <div class="mb-3">
      Fare Types :
    </div>
  </div>
  <div class="col-2">
    <div class="mb-3">
      <input class="form-check-input" type="checkbox" value="PUB" name="published" id="published" />
      <label for="published">Published</label>
    </div>
  </div>
  <div class="col-2">
    <div class="mb-3">
      <input class="form-check-input" type="checkbox" value="NET" name="net" id="net" />
      <label for="net">Net</label>
    </div>
  </div>
</div>

<div style="display: none;" id="show">
  <div class="mb-3">
    <label class="switch">
      <input type="checkbox" class="switch-input booking-show-agent" name="booking_show_agent" id="booking_show_agent" />
      <span class="switch-label p-0 pe-5">Show All Booking to Agent</span>
      <span class="switch-toggle-slider">
        <span class="switch-on">
          <i class="ti ti-check"></i>
        </span>
        <span class="switch-off">
          <i class="ti ti-x"></i>
        </span>
      </span>
    </label>
  </div>
  

<div class="row">
  <div class="col-6">
    <div class="mb-3 ">
      <label for="credit_limit" class="form-label">Credit Limit</label>
      <div class="input-group input-group-sm" style="margin-top: 0px;display: flex;width: 100%; ">
        
        <input type="text" class="input-group-text" style="border-top-right-radius: 0;border-bottom-right-radius: 0;width:20%;padding:8px;" name="currency" readonly value="CAD"></input>
        <input type="text" class="form-control numeral-mask credit-limit" id="credit_limit" name="agency_credit_limit" style="border-top-left-radius: 0; border-bottom-left-radius: 0;" placeholder="Credit Limit">
      </div>
    </div>
  </div>
  <div class="col-6">
    <div class="mb-3">
      <label for="email-id" class="form-label">Email Id</label>
      <input type="email" class="form-control numeral-mask" id="email-id" name="agency_email_id" maxlength="40" placeholder="Email" />
    </div>
  </div>
</div>
<div class="row">
  <div class="col-2">
    <div class="mb-3">
      <label for="country-number" class="form-label">Code</label>
      <select class="form-select" id="country-code" name="country_code">
        <option value="" selected disabled>Select</option>
        <?php $__currentLoopData = config('custom-app.country_code'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <option value="<?php echo e($value); ?>"><?php echo e($key); ?></option>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
      </select>
    </div>
  </div>
  <div class="col-4">
    <div class="mb-3">
      <label for="phone-number" class="form-label">Phone Number</label>
      <input type="text" class="form-control numeral-mask credit-card-mask" id="phone-number" name="agency_phone_number" maxlength="14" placeholder="Phone.No" />
    </div>
  </div>
  <div class="col-6">
    <div class="mb-3">
      <label for="postal-code" class="form-label">Postal Code</label>
      <input type="text" class="form-control numeral-mask postal_code" id="postal-code" name="postal_code" maxlength="10" placeholder="Postal Code" />
    </div>
  </div>
</div>
<div class="row">
  <div class="col-6">
    <div class="mb-3">
      <label for="agent-address" class="form-label">Address line 1</label>
      <input type="text" class="form-control" id="agent-address" name="agency_agent_address" maxlength="30" placeholder="Address Line 1">
    </div>
  </div>
  <div class="col-6">
    <div class="mb-3">
      <label for="agent-address-1" class="form-label">Address line 2</label>
      <input type="text" class="form-control" id="agent-address-1" name="agency_agent_address_1" maxlength="30" placeholder="Address Line 2">
    </div>
  </div>
</div>


<div class="row">
  <div class="col-6">
    <div class="mb-3">
      <label for="Country" class="form-label">Country</label>
      <select class="form-select" id="Country" name="country">
        <option value="" selected disabled>Select</option>
        <?php $__currentLoopData = config('custom-app.Regiter_country'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <option value="<?php echo e($value); ?>"><?php echo e($key); ?></option>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
      </select>
    </div>
  </div>
  <div class="col-6">
    <div class="mb-3">
      <label for="State" class="form-label">State</label>
      <select class="form-select" id="State" name="state">
        <option value="" selected disabled>Select</option>
        <?php $__currentLoopData = config('custom-app.Regiter_state'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <option value="<?php echo e($value); ?>"><?php echo e($key); ?></option>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
      </select>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-6">
    <div class="mb-3">
      <label for="City" class="form-label">City</label>
      <input type="text" class="form-control" id="City" name="city" placeholder="City" maxlength="20">
    </div>
  </div>
  <div class="col-6">
    <div class="mb-3">
      <label for="agency-timezone" class="form-label">Timezone</label>
      <div class="input-group">
        <input type="text" class="form-control" id="agency-timezone" name="agency_timezone" value="CA" readonly>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-6">
    <div class="mb-3">
      <label for="no_of_agents" class="form-label">No.of Agents</label>
      <input type="text" class="form-control no_of_agents" id="no_of_agents" name="no_of_agents" placeholder="No.of Agents" maxlength="5">
    </div>
  </div>
  <div class="col-3 mt-4 d-flex align-items-end" style="width:20%;">
    <div class="mb-3">
      <input class="form-check-input" type="checkbox" value="" name="iata_agency" id="Iata_ckeckbox" />
      <label for="Iata_ckeckbox">IATA Agency</label>
    </div>
  </div>
  <div class="col-3" style="width:30%;">
    <div class="mb-3">
      <label for="Iata_Number" class="form-label">IATA Number</label>
      <input type="text" class="form-control Iata_Number" id="Iata_Number" name="iata_number" placeholder="IATA Number" maxlength="12">
    </div>
  </div>
</div>
<fieldset class="p-3 border rounded agent-split mb-3">
  <div class="row">
    
    <div class="col-2">
      <div class="mb-3">
        <label for="Title" class="form-label">Title</label>
        <select class="form-select" id="Title" name="title" style="padding-right: 0px;padding-left: 7px;">
          <option value="" selected disabled>Select</option>
          <?php $__currentLoopData = config('custom-app.Title'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <option value="<?php echo e($value); ?>"><?php echo e($key); ?></option>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
      </div>
    </div>
    <div class="col-4">
      <div class="mb-3">
        <label for="First_name" class="form-label">First Name</label>
        <input type="text" class="form-control" id="First_name" name="first_name" placeholder="First Name" maxlength="15">
      </div>
    </div>
    <div class="col-6">
      <div class="mb-3">
        <label for="Last_name" class="form-label">Last Name</label>
        <input type="text" class="form-control" id="Last_name" name="last_name" placeholder="Last Name" maxlength="15">
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-2">
      <div class="mb-3">
        <label for="phone-number" class="form-label">Code</label>
        
        <select class="form-select" id="agency-country-code" name="agency_country_code">
          <option value="" selected disabled>Select</option>
          <?php $__currentLoopData = config('custom-app.country_code'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <option value="<?php echo e($value); ?>"><?php echo e($key); ?></option>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
      </div>
    </div>
    <div class="col-4">
      <div class="mb-3">
        <label for="phone-number" class="form-label">Phone Number</label>
        <input type="text" class="form-control numeral-mask agency_contact_phone_number" id="agency-phone-number" name="agency_contact_phone_number" maxlength="14" placeholder="Phone No" />
      </div>
    </div>
    <div class="col-6">
      <div class="mb-3">
        <label for="email-id" class="form-label">Email Id</label>
        <input type="email" class="form-control numeral-mask" id="agency-email-id" name="agency_contact_email_id" maxlength="40" placeholder="Email" />
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-12">
      <div class="">
        <label for="Designation" class="form-label">Designation</label>
        <select class="form-select" id="Designation" name="designation">
          <option value="" selected disabled>Select</option>
          <?php $__currentLoopData = config('custom-app.Designation'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <option value="<?php echo e($value); ?>"><?php echo e($key); ?></option>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
      </div>
    </div>
  </div>
</fieldset>




</div>
<input type="hidden" name="source" value="B2B">
<button type="submit" class="btn btn-primary">Submit</button>
<button type="button" class="btn btn-secondary" data-bs-dismiss="offcanvas">Cancel</button>
</form>
</div>
</div>


<script>
  document.addEventListener('change', function() {
    let agency_pos_val = document.getElementById("agency-pos").value;
    document.getElementById("pos_cl").value = agency_pos_val;
  })
</script>
<script>
  $(document).ready(function() {
    new Cleave(".credit-card-mask", {
      creditCard: true
    });
    new Cleave(".credit-card-masks", {
      creditCard: true
    });
    new Cleave(".credit-limit", {
      creditCard: true
    });
    new Cleave(".postal_code", {
      creditCard: true
    })
    new Cleave(".no_of_agents", {
      creditCard: true
    })
    new Cleave(".Iata_Number", {
      creditCard: true
    })
    new Cleave(".agency_country_code", {
      creditCard: true
    })
    new Cleave(".agency_contact_phone_number", {
      creditCard: true
    })
  });
</script>
<script>
  document.addEventListener("DOMContentLoaded", function() {
    const checkbox = document.getElementById('Iata_ckeckbox');
    const iataNumberInput = document.getElementById('Iata_Number');

    iataNumberInput.setAttribute('disabled', 'disabled');

    checkbox.addEventListener('change', function() {
      if (this.checked) {
        iataNumberInput.removeAttribute('disabled');
        checkbox.value = 1;

      } else {
        iataNumberInput.setAttribute('disabled', 'disabled');
        checkbox.value = 0;

      }
    })
  });
  document.addEventListener("DOMContentLoaded", function() {
    const toggleAgencyDetails = document.getElementById('toggle-agency-details');
    const booking_show_agent = document.getElementById('booking_show_agent');
    const showDiv = document.getElementById('show');
    const credit_limit = document.getElementById('credit_limit');
    const agent_address = document.getElementById('agent-address');
    const email_id = document.getElementById('email-id');
    const phone_number = document.getElementById('phone-number');
    const agency_timezone = document.getElementById('agency-timezone');
    const agency_pos = document.getElementById('agency-pos');
    const agency_group = document.getElementById('agency_group');
    const country_code = document.getElementById('country-code');
    const pos_cl = document.getElementById('pos_cl');
    const postal_code = document.getElementById('postal-code');
    const agent_address_1 = document.getElementById('agent-address-1');
    const Country = document.getElementById('Country');
    const State = document.getElementById('State');
    const City = document.getElementById('City');
    const no_of_agents = document.getElementById('no_of_agents');
    const Iata_ckeckbox = document.getElementById('Iata_ckeckbox');
    const Iata_Number = document.getElementById('Iata_Number');
    const Title = document.getElementById('Title');
    const Designation = document.getElementById('Designation');

    toggleAgencyDetails.addEventListener('change', function() {
      if (toggleAgencyDetails.checked) {
        showDiv.style.display = 'block';
        toggleAgencyDetails.value = 1;
        booking_show_agent.value = 1;
        Iata_ckeckbox.value = 0;
        agency_timezone.value = "CA";

      } else {
        showDiv.style.display = 'none';
        credit_limit.value = '';
        agent_address.value = '';
        email_id.value = '';
        phone_number.value = '';
        agency_timezone.value = '';
        country_code.value = '';
        pos_cl.value = '';
        postal_code.value = '';
        agent_address_1.value = '';
        Country.value = '';
        State.value = '';
        City.value = '';
        no_of_agents.value = '';
        Iata_ckeckbox.value = 1;
        Iata_Number.value = '';
        Title.value = '';
        Designation.value = '';
        pos_cl.value = '';
        booking_show_agent.value = 0;
      }
    });
  });
</script>



<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasEndEdit" aria-labelledby="offcanvasEndLabel" style="width:50%;">
  <div class="offcanvas-header">
    <h5 id="offcanvasEndLabel" class="offcanvas-title">Edit Agent Group</h5>
    <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
  </div>

  <div class="offcanvas-body mx-0 flex-grow-0">
    <form action="<?php echo e(route('backend.agentgroup.store')); ?>" method="POST" id="editForm">
      <?php echo csrf_field(); ?>
      <div class="row">
        <div class="col-6">
          <div class="input-group input-group-sm mb-3">
            <label for="group_name" class="form-label">Group Name</label>
            <div class=" input-group">
              <input type="text" class="form-control" id="edit_group_name" name="group_name" placeholder="Group Name" required>
            </div>
          </div>
        </div>
        <div class="col-6">
          <div class="mb-3 input-group input-group-sm">
            <label for="group_id" class="form-label">Group Id</label>
            
            <div class=" input-group">
              <input type="text" class="form-control" id="edit_agent_group_id" name="agent_group_id" required maxlength="10" placeholder="Group ID" disabled>
              
            </div>
          </div>
        </div>
      </div>
      <div id="agent_group_id-error-message" class="text-danger text-nowrap">
      </div>
      <div id="agent_group_id-spinner" class="spinner-border text-primary" style="width: 16px; height: 16px; display: none;" role="status">
        <span class="sr-only">Loading...</span>
      </div>
      <div class="col-6">
        <div class="mb-3">
          <label for="plan_name" class="form-label">Commission Plan</label>
          <select name="plan_name" id="edit_plan_name" class="form-select">
            <option value=""selected>Select a plan</option>
               <?php $__currentLoopData = $groupcommissionplan; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($item->group_commission_plan); ?>"><?php echo e($item->plan_name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
        </div>
      </div>
      <div class="card p-3">
        <div class="row">
          <div class="col" id="international"></div>
          <div class="col" id="domestic"></div>
        </div>
        <div class="row mt-3">
          <div class="col" id="transborder"></div>
        </div>
      </div>

      

      

      
<fieldset class="p-3 border rounded commission-split d-none">
  <label for="international_sharing_perc" class="form-label">International</label>

  <div class="row">
    <div class="col-md-5">
      <div class="input-group input-group-sm">

        <div class="input-group">
          <input type="text" class="form-control numeral-mask" id="edit_international_sharing_perc" name="international_sharing_perc" value="0" required maxlength="3">
          <span class="input-group-text"> % </span>
        </div>
      </div>
    </div>
    <div class="col-md-1 d-flex align-items-center justify-content-center">
      <i class="ti ti-plus" style="font-size: 2.25rem;color:#17224e !important;font-size: xx-large;"></i>
    </div>
    <div class="col-md-6">
      <div class="input-group input-group-sm">
        <div class="input-group">
          <span class="input-group-text">CAD</span>
          <input type="text" class="form-control numeral-mask" id="edit_international_sharing" name="international_sharing" value="0" required maxlength="3">
        </div>
      </div>
    </div>
  </div>
  <label for="domestic_sharing" class="form-label">Domestic Sharing</label>
  <div class="row">
    <div class="col-md-5">
      <div class="input-group input-group-sm">
        <div class=" input-group">
          <input type="text" class="form-control numeral-mask" id="edit_domestic_sharing_perc" name="domestic_sharing_perc" value="0" required maxlength="3">
          <span class="input-group-text"> % </span>
        </div>
      </div>
    </div>
    <div class="col-md-1 d-flex align-items-center justify-content-center">
      <i class="ti ti-plus" style="font-size: 2.25rem;color:#17224e !important;font-size: xx-large;"></i>
    </div>
    <div class="col-md-6">
      <div class="input-group input-group-sm">

        <div class=" input-group">
          <span class="input-group-text">CAD</span>
          <input type="text" class="form-control numeral-mask" id="edit_domestic_sharing" name="domestic_sharing" value="0" required maxlength="3">
        </div>
      </div>
    </div>
  </div>

  <label for="transporter_sharing" class="form-label">Transborder</label>
  <div class="row">
    <div class="col-md-5">
      <div class="input-group input-group-sm">

        <div class=" input-group">
          <input type="text" class="form-control numeral-mask" id="edit_transporter_sharing_perc" name="transporter_sharing_perc" value="0" required maxlength="3">
          <span class="input-group-text"> % </span>
        </div>

      </div>
    </div>
    <div class="col-md-1 d-flex align-items-center justify-content-center">
      <i class="ti ti-plus" style="font-size: 2.25rem;color:#17224e !important;font-size: xx-large;"></i>
    </div>
    <div class="col-md-6">
      <div class="input-group input-group-sm">

        <div class=" input-group">
          <span class="input-group-text">CAD</span>
          <input type="text" class="form-control numeral-mask" id="edit_transporter_sharing" name="transporter_sharing" value="0" required maxlength="3">

        </div>
      </div>
    </div>
  </div>
</fieldset>

<div class=" mb-3 input-group input-group-sm">
  
  <div class=" input-group">
    <input type="hidden" class="form-control" id="group_pos_id_edit" name="agency_pos" value="CA" readonly>
  </div>
</div>


<div class="row">
  <input type="checkbox" class="switch-input booking-show-agent" name="agency_group" id="edit_toggle-agency-details" />
  
  <div class="col-3">
    <div class="mb-3">
      Fare Types :
    </div>
  </div>
  <div class="col-3">
    <div class="mb-3">
      <input class="form-check-input" type="checkbox" value="" name="published" id="edit_Published" />
      <label for="edit_Published">Published</label>
    </div>
  </div>
  <div class="col-2">
    <div class="mb-3">
      <input class="form-check-input" type="checkbox" value="" name="net" id="edit_Net" />
      <label for="edit_Net">Net</label>
    </div>
  </div>
</div>
<input type="hidden" name="check" id="checkone">

<div style="display: none;" id="edit_show">
  <div class="mb-3">
    <label class="switch">
      <input type="checkbox" class="switch-input booking-show-agent" name="booking_show_agent" id="edit_booking_show_agent" />
      <span class="switch-label p-0 pe-5">Show All Booking to Agent</span>
      <span class="switch-toggle-slider">
        <span class="switch-on">
          <i class="ti ti-check"></i>
        </span>
        <span class="switch-off">
          <i class="ti ti-x"></i>
        </span>
      </span>
    </label>
  </div>
  <div class="row">
    <div class="col-6">
      <div class="mb-3 ">
        <label for="credit_limit" class="form-label">Credit Limit</label>
        <div class="input-group input-group-sm" style="margin-top: 0px;display: flex;width: 100%; ">
          <input type="text" class="input-group-text" style="border-top-right-radius: 0;border-bottom-right-radius: 0;width:20%;padding:8px;" id="edit_pos_cl" name="currency" readonly value="CAD"></input>
          
          <input type="text" class="form-control numeral-mask edit-credit-card-mask" id="edit_credit_limit" name="agency_credit_limit" style="border-top-left-radius: 0; border-bottom-left-radius: 0;" placeholder="Credit Limit">
        </div>
      </div>
    </div>
    <div class="col-6">
      <div class="mb-3">
        <label for="email-id" class="form-label">Email Id</label>
        <input type="email" class="form-control numeral-mask" id="edit_email_id" name="edit_agency_email_id" maxlength="40" placeholder="Email" />
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-2">
      <div class="mb-3">
        <label for="phone-number" class="form-label">Code</label>
        
        <select class="form-select" id="edit_country_code" name="country_code">
          <option value="+1">+1</option>
          
        </select>
      </div>
    </div>
    <div class="col-4">
      <div class="mb-3">
        <label for="phone-number" class="form-label">Phone Number</label>
        <input type="text" class="form-control numeral-mask edit-credit-limit" id="edit_phone_number" name="edit_agency_phone_number" maxlength="14" placeholder="Phone.No" />
      </div>
    </div>
    <div class="col-6">
      <div class="mb-3">
        <label for="postal-code" class="form-label">Postal Code</label>
        <input type="text" class="form-control numeral-mask edit_postal_code" id="edit_postal_code" name="postal_code" maxlength="10" placeholder="Postal Code" />
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-6">
      <div class="mb-3">
        <label for="agent-address" class="form-label">Address line 1</label>
        <input type="text" class="form-control" id="edit_agent_address" name="agency_agent_address" maxlength="30" placeholder="Address Line 1">
      </div>
    </div>
    <div class="col-6">
      <div class="mb-3">
        <label for="agent-address-1" class="form-label">Address line 2</label>
        <input type="text" class="form-control" id="edit_agent_address_1" name="agency_agent_address_1" maxlength="30" placeholder="Address Line 2">
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-6">
      <div class="mb-3">
        <label for="Country" class="form-label">Country</label>
        <select class="form-select" id="edit_country" name="country">
          <option value="" selected disabled>Select</option>
          <?php $__currentLoopData = config('custom-app.Regiter_country'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <option value="<?php echo e($value); ?>"><?php echo e($key); ?></option>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
      </div>
    </div>
    <div class="col-6">
      <div class="mb-3">
        <label for="State" class="form-label">State</label>
        <select class="form-select" id="edit_state" name="state">
          <option value="" selected disabled>Select</option>
          <?php $__currentLoopData = config('custom-app.Regiter_state'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <option value="<?php echo e($value); ?>"><?php echo e($key); ?></option>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-6">
      <div class="mb-3">
        <label for="City" class="form-label">City</label>
        <input type="text" class="form-control" id="edit_city" name="city" placeholder="City" maxlength="20">
      </div>
    </div>
    <div class="col-6">
      <div class="mb-3">
        <label for="agency-timezone" class="form-label">Timezone</label>
        <div class="input-group">
          <input type="text" class="form-control" id="group_timezone_id_edit" name="agency_timezone" value="CA" readonly>
        </div>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-6">
      <div class="mb-3">
        <label for="no_of_agents" class="form-label">No.of Agents</label>
        <input type="text" class="form-control edit_no_of_agents" id="edit_no_of_agents" name="no_of_agents" placeholder="No.of Agents" maxlength="5">
      </div>
    </div>
    <div class="col-3 mt-4 d-flex align-items-end" style="width:20%;">
      <div class="mb-3">
        <input class="form-check-input" type="checkbox" value="" name="iata_agency" id="edit_Iata_ckeckbox" />
        <label for="edit_Iata_ckeckbox">IATA Agency</label>
      </div>
    </div>
    <div class="col-3" style="width:30%;">
      <div class="mb-3">
        <label for="Iata_Number" class="form-label">IATA Number</label>
        <input type="text" class="form-control edit_Iata_Number" id="edit_Iata_Number" name="iata_number" placeholder="IATA Number" maxlength="12">
      </div>
    </div>
  </div>

  <fieldset class="p-3 border rounded agent-split mb-3">
    <div class="row">
      
      <div class="col-2">
        <div class="mb-3">
          <label for="Title" class="form-label">Title</label>
          <select class="form-select" id="edit_Title" name="title" style="padding-right: 0px;padding-left: 7px;">
            <option value="" selected disabled>Select</option>
            <?php $__currentLoopData = config('custom-app.Title'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <option value="<?php echo e($value); ?>"><?php echo e($key); ?></option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          </select>
        </div>
      </div>
      <div class="col-4">
        <div class="mb-3">
          <label for="First_name" class="form-label">First Name</label>
          <input type="text" class="form-control" id="edit_First_name" name="first_name" placeholder="First Name" maxlength="15">
        </div>
      </div>
      <div class="col-6">
        <div class="mb-3">
          <label for="Last_name" class="form-label">Last Name</label>
          <input type="text" class="form-control" id="edit_Last_name" name="last_name" placeholder="Last Name" maxlength="15">
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-2">
        <div class="mb-3">
          <label for="phone-number" class="form-label">Code</label>
          
          <select class="form-select" id="edit-agency-country-code" name="agency_country_code">
            <option value="+1">+1</option>
            
          </select>
        </div>
      </div>
      <div class="col-4">
        <div class="mb-3">
          <label for="phone-number" class="form-label">Phone Number</label>
          <input type="text" class="form-control numeral-mask edit_agency_contact_phone_number" id="edit-agency-phone-number" name="agency_contact_phone_number" maxlength="14" placeholder="Phone No" />
        </div>
      </div>
      <div class="col-6">
        <div class="mb-3">
          <label for="email-id" class="form-label">Email Id</label>
          <input type="email" class="form-control numeral-mask" id="edit-agency-email-id" name="agency_contact_email_id" maxlength="40" placeholder="Email" />
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-12">
        <div class="">
          <label for="Designation" class="form-label">Designation</label>
          <select class="form-select" id="edit_Designation" name="designation">
            
            <?php $__currentLoopData = config('custom-app.Designation'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <option value="<?php echo e($value); ?>"><?php echo e($key); ?></option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          </select>
        </div>
      </div>
    </div>
  </fieldset>

  
</div>
<input type="hidden" name="source" value="B2B">
<button type="submit" class="btn btn-primary">Submit</button>
<button type="button" class="btn btn-secondary" data-bs-dismiss="offcanvas">Cancel</button>
</form>
<div class="" id="getInvoicesHeader"></div>
<?php echo $__env->make('backend.agentgroup.invoiceheader', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<div class="" id="getSignatureDataInvoices"></div>
<?php echo $__env->make('backend.agentgroup.invoicesignature', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<div class="" id="getSignatureDatas"></div>
<?php echo $__env->make('backend.agentgroup.emailsignature', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<div class="" id="getMailConfiguration"></div>
<?php echo $__env->make('backend.agentgroup.mailConfigurationForm', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</div>
</div>
    <?php echo $__env->make('backend.agencygroup.creditLimit', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


<script>
  document.addEventListener('change', function() {
    let agency_pos_val = document.getElementById("group_pos_id_edit").value;
    document.getElementById("edit_pos_cl").value = "CAD";
  })
</script>
<script>
  $(document).ready(function() {
    new Cleave(".edit-credit-card-mask", {
      creditCard: true
    });
    new Cleave(".edit-credit-card-masks", {
      creditCard: true
    });
    new Cleave(".edit-credit-limit", {
      creditCard: true
    });
    new Cleave(".edit_postal_code", {
      creditCard: true
    })
    new Cleave(".edit_no_of_agents", {
      creditCard: true
    })
    new Cleave(".edit_Iata_Number", {
      creditCard: true
    })
    new Cleave(".edit_agency_country_code", {
      creditCard: true
    })
    new Cleave(".edit_agency_contact_phone_number", {
      creditCard: true
    })
  });
</script>

<script>
  const editCheckbox = document.getElementById('edit_Iata_ckeckbox');
  const editIataNumberInput = document.getElementById('edit_Iata_Number');
  editCheckbox.addEventListener('change', function() {
    if (this.checked) {
      editIataNumberInput.removeAttribute('disabled');
      editCheckbox.value = 1;
    } else {
      editIataNumberInput.setAttribute('disabled', 'disabled');
      editCheckbox.value = 0;
    }
  });

  //     document.addEventListener("DOMContentLoaded",function(){
  //    const editCheckbox = document.getElementById('edit_Iata_ckeckbox');
  //    const editIataNumberInput = document.getElementById('edit_Iata_Number');

  //    editIataNumberInput.setAttribute('disabled', 'disabled');

  //    editCheckbox.addEventListener('change', function() {
  //      if (this.checked) {
  //       editIataNumberInput.removeAttribute('disabled');

  //       } else {
  //         editIataNumberInput.setAttribute('disabled', 'disabled');
  //       }
  //     })
  // });
</script>
<script>
  document.addEventListener("DOMContentLoaded", function() {
    const toggleAgencyDetails = document.getElementById('edit_toggle-agency-details');
    const bookingAgentDetails = document.getElementById('edit_booking_show_agent');
    const booking_show_agent = document.getElementById('booking_show_agent');
    const showDiv = document.getElementById('edit_show');
    const credit_limit = document.getElementById('credit_limit');
    const agent_address = document.getElementById('agent-address');
    const email_id = document.getElementById('email-id');
    const phone_number = document.getElementById('phone-number');
    const agency_timezone = document.getElementById('group_timezone_id_edit');
    const agency_pos = document.getElementById('agency-pos');
    const agency_group = document.getElementById('agency_group');
    const country_code = document.getElementById('country-code');
    const pos_cl = document.getElementById('pos_cl');
    let netDetails = document.getElementById('edit_Net');
    let publishedDetails = document.getElementById('edit_Published');



    bookingAgentDetails.addEventListener('change', function() {
      if (bookingAgentDetails.checked) {
        bookingAgentDetails.value = 1;
      } else {
        bookingAgentDetails.value = 0;
      }
    })
    netDetails.addEventListener('change', function() {
      if (netDetails.checked) {
        netDetails.value = "NET";
      } else {
        netDetails.value = null;
      }
    })
    publishedDetails.addEventListener('change', function() {
      if (publishedDetails.checked) {
        publishedDetails.value = "PUB";
      } else {
        publishedDetails.value = null;
      }
    })
    toggleAgencyDetails.addEventListener('change', function() {
      if (toggleAgencyDetails.checked) {
        showDiv.style.display = 'block';
        toggleAgencyDetails.value = 1;
        // booking_show_agent.value = 1;
        agency_timezone.value = "CA";

      } else {
        showDiv.style.display = 'none';
        credit_limit.value = '';
        agent_address.value = '';
        email_id.value = '';
        phone_number.value = '';
        agency_timezone.value = '';
        country_code.value = '';
        pos_cl.value = '';
        // booking_show_agent.value = 0;
      }
    });
  });
</script>

<div class="nav-tabs-shadow nav-align-top mt-2">
  <div class="d-flex justify-content-between" >
    <ul class="nav nav-tabs" role="tablist" style="width: 100% !important;position:relative;">
      <li class="nav-item" >
        <button type="button" class="nav-link active" role="tab" data-bs-toggle="tab" data-bs-target="#navs-top-list" aria-controls="navs-top-list" aria-selected="true">List</button>
      </li>
      <li class="nav-item" style="display: none">
        <button type="button" class="nav-link" role="tab" data-bs-toggle="tab" data-bs-target="#navs-top-archived" aria-controls="navs-top-archived" aria-selected="false">Archived</button>
      </li>
    </ul>
    <div style="position:absolute;right:40px;z-index:999;" class="mt-1">
      <button type="button" class="btn btn-sm btn-primary btn-icon rounded-pill dropdown-toggle hide-arrow"
                        data-bs-toggle="dropdown">
                        <i class="ti ti-dots-vertical"></i>
                    </button>
                    <ul class="dropdown-menu">
                      <li><span class="dropdown-header">Options</span></li>
                      <hr class="dropdown-divider">
                      <li><a href="#" class="archive dropdown-item">Show Archived</a></li>
                      

                  </ul>
    </div>

  </div>
  <div class="tab-content">
    <div class="tab-pane fade show active" id="navs-top-list" role="tabpanel">
      <div>
        <table class="table dt-advanced-search-2" id="agentGroupTable">
          <thead>
            <tr>
              <th>Id</th>
              <th>Group Name</th>
              <th>Plan Name</th>
              <th>AGENCY TYPE</th>
              <th>AGENT</th>
              <th>CREDIT LIMIT</th>
              <th>Actions</th>
            </tr>
          </thead>
        </table>
      </div>
    </div>
    <div class="tab-pane fade" id="navs-top-archived" role="tabpanel">
      <div>
        <table class="table dt-advanced-search-3">
          <thead>
            <tr>
              <th>Id</th>
              <th>Group Name</th>
              <th>Plan Name</th>
              <th>AGENCY TYPE</th>
              <th>AGENT</th>
              <th>CREDIT LIMIT</th>
              <th>Actions</th>
            </tr>
          </thead>
        </table>
      </div>
    </div>
  </div>
</div>



<script>
  function editAgent(data) {
    console.log(data)
    console.log('int',data.international_sharing_perc,data.international_sharing,'dom',data.domestic_sharing_perc,data.domestic_sharing,'tran',data.transporter_sharing_perc,data.transporter_sharing);

    var form = document.getElementById('editForm');
    form.action = '/admin/agentgroup/update/' + data.id;
    document.getElementById('edit_group_name').value = data.group_name;
    document.getElementById('edit_plan_name').value = data.plan_name;
    document.getElementById('edit_agent_group_id').value = data.agent_group_id;
    // document.getElementById('edit_international_sharing').value = data.international_sharing;
    // document.getElementById('edit_domestic_sharing').value = data.domestic_sharing;
    // document.getElementById('edit_transporter_sharing').value = data.transporter_sharing;
    // document.getElementById('edit_international_sharing_perc').value = data.international_sharing_perc;
    // document.getElementById('edit_domestic_sharing_perc').value = data.domestic_sharing_perc;
    // document.getElementById('edit_transporter_sharing_perc').value = data.transporter_sharing_perc;
    document.getElementById('edit_credit_limit').value = data.agency_credit_limit;
    document.getElementById('edit_agent_address').value = data.agency_agent_address;
    document.getElementById('edit_email_id').value = data.agency_email_id;
    // document.getElementById('edit_country_code').value = data.country_code;
    document.getElementById('edit_phone_number').value = data.agency_phone_number;
    document.getElementById('edit_pos_cl').value = data.currency;
    document.getElementById('edit_postal_code').value = data.postal_code;
    document.getElementById('edit_agent_address_1').value = data.agency_agent_address_1;
    document.getElementById('edit_city').value = data.city;
    document.getElementById('edit_no_of_agents').value = data.no_of_agents;
    document.getElementById('edit_country').value = data.country;
    document.getElementById('edit_state').value = data.state;
    // document.getElementById('edit_Iata_ckeckbox').checked = data.iata_agency == 1;
    document.getElementById('edit_Title').value = data.title;
    document.getElementById('edit_First_name').value = data.first_name;
    document.getElementById('edit_Last_name').value = data.last_name;
    // document.getElementById('edit-agency-country-code').value = data.agency_country_code;
    document.getElementById('edit-agency-phone-number').value = data.agency_phone_no;
    document.getElementById('edit-agency-email-id').value = data.agency_email;
    document.getElementById('edit_Designation').value = data.designation;
    document.getElementById('edit_Iata_Number').value = data.iata_number;
    let net_value = document.getElementById('edit_Net');
    let published_value = document.getElementById('edit_Published');
    document.getElementById('international').innerHTML='International Commission: '+(data.international_sharing_perc || '0') +' % + CAD '+ (data.international_sharing || '0.00');
    document.getElementById('domestic').innerHTML='Domestic Commission: ' + (data.domestic_sharing_perc || '0')+' % + CAD '+ (data.domestic_sharing || '0.00');
    document.getElementById('transborder').innerHTML='Transborder Commission: ' + (data.transporter_sharing_perc || '0')+' % + CAD '+ (data.transporter_sharing || '0.00');



    document.getElementById('group_pos_id_edit').value = data.agency_pos;
    document.getElementById('group_timezone_id_edit').value = data.agency_timezone;


    document.getElementById('name-in-template').innerHTML = data.group_name;
    document.getElementById('group-id-template').value = data.group_id;
    document.getElementById('getSignatureDatas').innerHTML = `
            <button type="button" class="btn btn-primary mt-2 test"
                data-bs-toggle="modal" data-bs-target="#backDropModalEmail"
                onclick="getSignatureData('${data.group_id}')"
                id="getSignatureData" data-id="${data.group_id}">
                Email Signature
            </button>
        `;


    document.getElementById('name-in-template-invoice').innerHTML = data.group_name;
    document.getElementById('group-id-template-invoice').value = data.group_id;
    document.getElementById('getSignatureDataInvoices').innerHTML = `
            <button type="button" class="btn btn-primary mt-2 test"
                data-bs-toggle="modal" data-bs-target="#backDropModalInvoice"
                onclick="getSignatureDataInvoice('${data.group_id}')"
                id="getSignatureDataInvoice" data-id="${data.group_id}">
                Email Header
            </button>
        `;
        document.getElementById('configGroupName').innerHTML = data.group_name;
    document.getElementById('getMailConfiguration').innerHTML = `
            <button type="button" class="btn btn-primary mt-2 test"
                data-bs-toggle="modal" data-bs-target="#backDropModalMailConfiguration"
                onclick="getSignatureDataMailConf('${data.group_id}')"
                id="mailConfiguration" data-id="${data.group_id}">
                Mail Configuration
            </button>
        `;


        document.getElementById('group-id-invoice-header').value = data.group_id;
        document.getElementById('group-name-invoice-header').innerHTML = data.group_name;
        document.getElementById('getInvoicesHeader').innerHTML = `
            <button type="button" class="btn btn-primary mt-2 test"
                data-bs-toggle="modal" data-bs-target="#backDropModalGetInvoicesHeader"
                onclick="getDataInvoicesHeader('${data.group_id}')"
                id="getInvoicesHeaders" data-id="${data.group_id}">
                Invoice Header
            </button>
        `;



    document.getElementById('mail-group-id').value = data.group_id;
    document.getElementById('mail-group-id-default').value = data.group_id;
    document.getElementById('mail-group-id-test').value = data.group_id;



    // let allowed_fare_type_value = data.allowed_fare_type;
    // if(allowed_fare_type_value==='NET'){
    //   net_value.checked=true;
    //   published_value.checked=false;
    //   net_value.value="NET";
    // }
    // else if(allowed_fare_type_value==='PUB'){
    //   published_value.checked=true;
    //   published_value.value="PUB";
    //   net_value.checked=false;
    // }
    // if(allowed_fare_type_value==='PUB,NET'){
    //   net_value.checked=true;
    //   net_value.value="NET";
    //   published_value.checked=true;
    //   published_value.value="PUB";
    // }
    // if(allowed_fare_type_value===null){
    //   net_value.checked=false;
    //   net_value.value=null;
    //   published_value.checked=false;
    //   published_value.value=null;
    // }
    let allowed_fare_type_value = data.allowed_fare_type;
    net_value.checked = false;
    published_value.checked = false;
    net_value.value = null;
    published_value.value = null;

    if (allowed_fare_type_value === 'NET') {
      net_value.checked = true;
      net_value.value = "NET";
    } else if (allowed_fare_type_value === 'PUB') {
      published_value.checked = true;
      published_value.value = "PUB";
    } else if (allowed_fare_type_value === 'PUB,NET') {
      net_value.checked = true;
      net_value.value = "NET";
      published_value.checked = true;
      published_value.value = "PUB";
    }

    // If allowed_fare_type_value is null, checkboxes remain unchecked, as reset is done at the start.


    var activeCheckbox = document.getElementById('edit_toggle-agency-details');
    var activeCheckboxAllBooking = document.getElementById('edit_booking_show_agent');
    var activeIayataCheckbox = document.getElementById('edit_Iata_ckeckbox');
    var activeIayataNumber = document.getElementById('edit_Iata_Number');
    const creditLimit = document.getElementById('edit_credit_limit')
    const addressInput = document.getElementById('edit_agent_address')
    const emailInput = document.getElementById('edit_email_id')
    // const countryCodeInput = document.getElementById('edit_country_code')
    const phoneNumberInput = document.getElementById('edit_phone_number');
    const pos_cl = document.getElementById('edit_pos_cl');


    if (data.booking_show_agent == 1) {
      activeCheckboxAllBooking.checked = true;
      activeCheckboxAllBooking.value = 1;
      const showDiv = document.getElementById('edit_show');
      showDiv.style.display = 'block';
    } else {
      const showDiv = document.getElementById('edit_show');
      showDiv.style.display = 'none';
      activeCheckboxAllBooking.checked = false;
      activeCheckboxAllBooking.value = 0;
    }

    if (data.iata_agency == 1) {
      activeIayataCheckbox.checked = true;
      activeIayataCheckbox.value = 1;
      activeIayataNumber.disabled = false;
    } else {
      activeIayataCheckbox.checked = false;
      activeIayataCheckbox.value = 0;
      activeIayataNumber.disabled = true;
    }


    if (data.agency_group == 1) {
      activeCheckbox.checked = true;
      activeCheckbox.value = 1;
      const showDiv = document.getElementById('edit_show');
      showDiv.style.display = 'block';
      activeCheckbox.disabled = true;
      pos_cl.readOnly = true;
      document.getElementById('checkone').value = "1";
      pos_cl.value = "CAD";
    } else {
      const showDiv = document.getElementById('edit_show');
      document.getElementById('checkone').value = "0";
      showDiv.style.display = 'none';
      activeCheckbox.checked = false;
      activeCheckbox.value = 0;
      activeCheckbox.disabled = false;
      pos_cl.readOnly = false;
    }
  }
</script>

<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
<script>
  $(document).ready(function() {
    $("#successMessage").fadeIn('fast').delay(3000).fadeOut('slow');
  });
  $(document).ready(function() {
    $("#error").fadeIn('fast').delay(3000).fadeOut('slow');
  });
</script>

<script>
  $(document).ready(function() {
    new Cleave(".numeral-mask", {
      numeral: true,
      numeralThousandsGroupStyle: "none"
    });
  });
</script>

<script>
  $(document).ready(function() {
    let timeout = null;
    $('#agent_group_id').on('blur', function() {
      clearTimeout(timeout);
      timeout = setTimeout(function() {
        var agent_group_id = $('#agent_group_id').val();
        $('#agent_group_id-spinner').show();
        $.ajax({
          url: '<?php echo e(route("check.agent_group_id")); ?>',
          method: 'POST',
          data: {
            _token: '<?php echo e(csrf_token()); ?>',
            agent_group_id: agent_group_id
          },
          success: function(response) {
            $('#agent_group_id-spinner').hide();
            if (response.exists) {
              $('#agent_group_id-error-message').text('This agent group ID already exists in the database.');
              setTimeout(() => {
                $('#agent_group_id-error-message').text('');
              }, 10000);
            } else {
              $('#agent_group_id-error-message').text('');
            }
          },
        });
      }, 500);
    });

    $('#agent_group_id').on('input', function() {
      clearTimeout(timeout);
      $('#agent_group_id-error-message').text('');
    });
  });
</script>
<script>
  function getSignatureDataMailConf(groupId) {
    $.ajax({
      url: "<?php echo e(route('backend.mailConfigurationBtn')); ?>",
      type: 'POST',
      data: {
        _token: '<?php echo e(csrf_token()); ?>',
        groupId: groupId
      },
      success: function(response) {
        var checkGroupId = response.checkGroupId;
        mailConfiguration(checkGroupId);

        setTimeout(function() {
          $('#response-message').html('');
        }, 3000);
      },
      error: function(xhr) {
        var errorMessage = xhr.responseJSON && xhr.responseJSON.message
                           ? xhr.responseJSON.message
                           : 'An unknown error occurred.';
        $('#response-message').html('<div class="alert alert-danger"><p>Error: ' + errorMessage + '</p></div>');
        setTimeout(function() {
          $('#response-message').html('');
        }, 3000);
      }
    });
  }

  function mailConfiguration(data) {
    console.log(data);

    const checkbox = document.getElementById('checkConfig');


    if (data && data.custom_email_config !== undefined) {
      $('#checkConfig').prop('checked', data.custom_email_config == '1');
    } else {
      $('#checkConfig').prop('checked', false);
      $('.defaultenv').show()
    }

    $('.mainContainer').hide();
    $('.defaultenv').show()

    $('#checkConfig').change(function(){
      if ($(this).is(':checked')) {
        $('.mainContainer').show();
        $('.defaultenv').hide();
      } else {
        $('.mainContainer').hide();
        $('.defaultenv').show();
      }
    });

    if ($('#checkConfig').is(':checked')) {
      $('.mainContainer').show();
      $('.defaultenv').hide();

    }

    if (data) {
      document.getElementById('mail_mailer_text').innerHTML = data.mailer ?? '';
      document.getElementById('mail_host_text').innerHTML = data.host ?? '';
      document.getElementById('mail_port_text').innerHTML = data.port ?? '';
      document.getElementById('mail_user_name_text').innerHTML = data.username ?? '';
      document.getElementById('mail_password_text').innerHTML = data.password ?? '';
      document.getElementById('mail_encryption_text').innerHTML = data.encryption ?? '';
      document.getElementById('mail_from_address_text').innerHTML = data.from_address ?? '';
      document.getElementById('mail_from_name_text').innerHTML = data.from_name ?? '';
      document.getElementById('mail_bcc_address_text').innerHTML = data.bcc_address ?? '';
      document.getElementById('mail_cc_address_text').innerHTML = data.cc_address ?? '';

      document.querySelector('#mailConfigForm').style.display = "none";
      document.querySelector('#text-static-container').style.display = "block";
      document.querySelector('#edit-Form').style.display = "block";

      document.getElementById('mail_mailer').value = data.mailer ?? '';
      document.getElementById('mail_host').value = data.host ?? '';
      document.getElementById('mail_port').value = data.port ?? '';
      document.getElementById('mail_user_name').value = data.username ?? '';
      document.getElementById('mail_password').value = data.password ?? '';
      document.getElementById('mail_encryption').value = data.encryption ?? '';
      document.getElementById('mail_from_address').value = data.from_address ?? '';
      document.getElementById('mail_from_name').value = data.from_name ?? '';
      document.getElementById('mail_bcc_address').value = data.bcc_address ?? '';
      document.getElementById('mail_cc_address').value = data.cc_address ?? '';
    } else {
      document.getElementById('mail_mailer_text').innerHTML = '';
      document.getElementById('mail_host_text').innerHTML = '';
      document.getElementById('mail_port_text').innerHTML = '';
      document.getElementById('mail_user_name_text').innerHTML = '';
      document.getElementById('mail_password_text').innerHTML = '';
      document.getElementById('mail_encryption_text').innerHTML = '';
      document.getElementById('mail_from_address_text').innerHTML = '';
      document.getElementById('mail_from_name_text').innerHTML = '';
      document.getElementById('mail_bcc_address_text').innerHTML = '';
      document.getElementById('mail_cc_address_text').innerHTML = '';

      document.querySelector('#mailConfigForm').style.display = "block";
      document.querySelector('#text-static-container').style.display = "none";
      document.querySelector('#edit-Form').style.display = "none";

      document.getElementById('mail_mailer').value = '';
      document.getElementById('mail_host').value = '';
      document.getElementById('mail_port').value = '';
      document.getElementById('mail_user_name').value = '';
      document.getElementById('mail_password').value = '';
      document.getElementById('mail_encryption').value = '';
      document.getElementById('mail_from_address').value = '';
      document.getElementById('mail_from_name').value = '';
      document.getElementById('mail_bcc_address').value = '';
      document.getElementById('mail_cc_address').value = '';
    }
  }
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts/layoutMaster', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\crm-ct\crm-ctt\resources\views/backend/agentgroup/index.blade.php ENDPATH**/ ?>