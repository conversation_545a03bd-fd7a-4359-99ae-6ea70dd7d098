<?php

use App\Events\emailEvents;
use App\Events\triggerMessageEvent;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Backend\UserController;
use App\Http\Controllers\Backend\AgentController;
use App\Http\Controllers\Backend\AirlinesBaggage;
use App\Http\Controllers\Backend\AgencyController;
use App\Http\Controllers\Backend\FlightController;
use App\Http\Controllers\Backend\GetApiController;
use App\Http\Controllers\Backend\AppChatController;
use App\Http\Controllers\Backend\CarrierController;
use App\Http\Controllers\Backend\B2bAgentController;
use App\Http\Controllers\Backend\AgentAuthController;
use App\Http\Controllers\Backend\B2cPortalController;
use App\Http\Controllers\Backend\SignatureController;
use App\Http\Controllers\Backend\AgentNotesController;
use App\Http\Controllers\Backend\CommissionPlanController;
use App\Http\Controllers\Backend\MetaportalController;
use App\Http\Controllers\Backend\MetasectorController;
use App\Http\Controllers\Backend\ScreenshotController;
use App\Http\Controllers\Backend\PnrRetrieveController;
use App\Http\Controllers\Backend\FeeruleGroupController;
use App\Http\Controllers\Backend\MetaportalIdController;
use App\Http\Controllers\laravel_example\UserManagement;
use App\Http\Controllers\Backend\B2bAgentGroupController;
use App\Http\Controllers\Backend\PortalFeeruleController;
use App\Http\Controllers\Backend\CannedMessagerController;
use App\Http\Controllers\Backend\PortalCarriersController;
use App\Http\Controllers\Backend\PortalTemplateController;
use App\Http\Controllers\Backend\GflightsfeeruleController;

use App\Http\Controllers\Backend\ChatConversationController;
use App\Http\Controllers\Backend\GflightsLocationController;
use App\Http\Controllers\Backend\AirlineRepriceFareController;
use App\Http\Controllers\Backend\SupportController;

use App\Http\Controllers\Backend\AirlineBlockController;
use App\Http\Controllers\Backend\ImportRouteBlockingController;
use App\Http\Controllers\Backend\B2cUserController;
use App\Http\Controllers\Backend\PortalConfigController;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

require __DIR__.'/auth.php';

$controller_path = 'App\Http\Controllers';

// Main Page Route
Route::group(['middleware' => ['web', 'auth'],], function () {
    $controller_path = 'App\Http\Controllers';
Route::get('/', $controller_path . '\dashboard\Analytics@index')->name('dashboard');
});
Route::get('/dashboard/analytics', $controller_path . '\dashboard\Analytics@index')->name('dashboard-analytics');
Route::get('/dashboard/crm', $controller_path . '\dashboard\Crm@index')->name('dashboard-crm');
Route::get('/dashboard/ecommerce', $controller_path . '\dashboard\Ecommerce@index')->name('dashboard-ecommerce');

// locale
Route::get('lang/{locale}', $controller_path . '\language\LanguageController@swap');

// layout
Route::get('/layouts/collapsed-menu', $controller_path . '\layouts\CollapsedMenu@index')->name('layouts-collapsed-menu');
Route::get('/layouts/content-navbar', $controller_path . '\layouts\ContentNavbar@index')->name('layouts-content-navbar');
Route::get('/layouts/content-nav-sidebar', $controller_path . '\layouts\ContentNavSidebar@index')->name('layouts-content-nav-sidebar');
Route::get('/layouts/navbar-full', $controller_path . '\layouts\NavbarFull@index')->name('layouts-navbar-full');
Route::get('/layouts/navbar-full-sidebar', $controller_path . '\layouts\NavbarFullSidebar@index')->name('layouts-navbar-full-sidebar');
Route::get('/layouts/horizontal', $controller_path . '\layouts\Horizontal@index')->name('layouts-horizontal');
Route::get('/layouts/vertical', $controller_path . '\layouts\Vertical@index')->name('layouts-vertical');
Route::get('/layouts/without-menu', $controller_path . '\layouts\WithoutMenu@index')->name('layouts-without-menu');
Route::get('/layouts/without-navbar', $controller_path . '\layouts\WithoutNavbar@index')->name('layouts-without-navbar');
Route::get('/layouts/fluid', $controller_path . '\layouts\Fluid@index')->name('layouts-fluid');
Route::get('/layouts/container', $controller_path . '\layouts\Container@index')->name('layouts-container');
Route::get('/layouts/blank', $controller_path . '\layouts\Blank@index')->name('layouts-blank');

// apps
Route::get('/app/email', $controller_path . '\apps\Email@index')->name('app-email');
Route::get('/app/chat', $controller_path . '\apps\Chat@index')->name('app-chat');
Route::get('/app/calendar', $controller_path . '\apps\Calendar@index')->name('app-calendar');
Route::get('/app/kanban', $controller_path . '\apps\Kanban@index')->name('app-kanban');
Route::get('/app/invoice/list', $controller_path . '\apps\InvoiceList@index')->name('app-invoice-list');
Route::get('/app/invoice/preview', $controller_path . '\apps\InvoicePreview@index')->name('app-invoice-preview');
Route::get('/app/invoice/print', $controller_path . '\apps\InvoicePrint@index')->name('app-invoice-print');
Route::get('/app/invoice/edit', $controller_path . '\apps\InvoiceEdit@index')->name('app-invoice-edit');
Route::get('/app/invoice/add', $controller_path . '\apps\InvoiceAdd@index')->name('app-invoice-add');
Route::get('/app/user/list', $controller_path . '\apps\UserList@index')->name('app-user-list');
Route::get('/app/user/view/account', $controller_path . '\apps\UserViewAccount@index')->name('app-user-view-account');
Route::get('/app/user/view/security', $controller_path . '\apps\UserViewSecurity@index')->name('app-user-view-security');
Route::get('/app/user/view/billing', $controller_path . '\apps\UserViewBilling@index')->name('app-user-view-billing');
Route::get('/app/user/view/notifications', $controller_path . '\apps\UserViewNotifications@index')->name('app-user-view-notifications');
Route::get('/app/user/view/connections', $controller_path . '\apps\UserViewConnections@index')->name('app-user-view-connections');
Route::get('/app/access-roles', $controller_path . '\apps\AccessRoles@index')->name('app-access-roles');
Route::get('/app/access-permission', $controller_path . '\apps\AccessPermission@index')->name('app-access-permission');

// pages
Route::get('/pages/profile-user', $controller_path . '\pages\UserProfile@index')->name('pages-profile-user');
Route::get('/pages/profile-teams', $controller_path . '\pages\UserTeams@index')->name('pages-profile-teams');
Route::get('/pages/profile-projects', $controller_path . '\pages\UserProjects@index')->name('pages-profile-projects');
Route::get('/pages/profile-connections', $controller_path . '\pages\UserConnections@index')->name('pages-profile-connections');
Route::get('/pages/account-settings-account', $controller_path . '\pages\AccountSettingsAccount@index')->name('pages-account-settings-account');
Route::get('/pages/account-settings-security', $controller_path . '\pages\AccountSettingsSecurity@index')->name('pages-account-settings-security');
Route::get('/pages/account-settings-billing', $controller_path . '\pages\AccountSettingsBilling@index')->name('pages-account-settings-billing');
Route::get('/pages/account-settings-notifications', $controller_path . '\pages\AccountSettingsNotifications@index')->name('pages-account-settings-notifications');
Route::get('/pages/account-settings-connections', $controller_path . '\pages\AccountSettingsConnections@index')->name('pages-account-settings-connections');
Route::get('/pages/faq', $controller_path . '\pages\Faq@index')->name('pages-faq');
Route::get('/pages/help-center-landing', $controller_path . '\pages\HelpCenterLanding@index')->name('pages-help-center-landing');
Route::get('/pages/help-center-categories', $controller_path . '\pages\HelpCenterCategories@index')->name('pages-help-center-categories');
Route::get('/pages/help-center-article', $controller_path . '\pages\HelpCenterArticle@index')->name('pages-help-center-article');
Route::get('/pages/pricing', $controller_path . '\pages\Pricing@index')->name('pages-pricing');
Route::get('/pages/misc-error', $controller_path . '\pages\MiscError@index')->name('pages-misc-error');
Route::get('/pages/misc-under-maintenance', $controller_path . '\pages\MiscUnderMaintenance@index')->name('pages-misc-under-maintenance');
Route::get('/pages/misc-comingsoon', $controller_path . '\pages\MiscComingSoon@index')->name('pages-misc-comingsoon');
Route::get('/pages/misc-not-authorized', $controller_path . '\pages\MiscNotAuthorized@index')->name('pages-misc-not-authorized');

// authentication
Route::get('/auth/login-basic', $controller_path . '\authentications\LoginBasic@index')->name('auth-login-basic');
Route::get('/auth/login-cover', $controller_path . '\authentications\LoginCover@index')->name('auth-login-cover');
Route::get('/auth/register-basic', $controller_path . '\authentications\RegisterBasic@index')->name('auth-register-basic');
Route::get('/auth/register-cover', $controller_path . '\authentications\RegisterCover@index')->name('auth-register-cover');
Route::get('/auth/register-multisteps', $controller_path . '\authentications\RegisterMultiSteps@index')->name('auth-register-multisteps');
Route::get('/auth/verify-email-basic', $controller_path . '\authentications\VerifyEmailBasic@index')->name('auth-verify-email-basic');
Route::get('/auth/verify-email-cover', $controller_path . '\authentications\VerifyEmailCover@index')->name('auth-verify-email-cover');
Route::get('/auth/reset-password-basic', $controller_path . '\authentications\ResetPasswordBasic@index')->name('auth-reset-password-basic');
Route::get('/auth/reset-password-cover', $controller_path . '\authentications\ResetPasswordCover@index')->name('auth-reset-password-cover');
Route::get('/auth/forgot-password-basic', $controller_path . '\authentications\ForgotPasswordBasic@index')->name('auth-forgot-password-basic');
Route::get('/auth/forgot-password-cover', $controller_path . '\authentications\ForgotPasswordCover@index')->name('auth-forgot-password-cover');
Route::get('/auth/two-steps-basic', $controller_path . '\authentications\TwoStepsBasic@index')->name('auth-two-steps-basic');
Route::get('/auth/two-steps-cover', $controller_path . '\authentications\TwoStepsCover@index')->name('auth-two-steps-cover');

// wizard example
Route::get('/wizard/ex-checkout', $controller_path . '\wizard_example\Checkout@index')->name('wizard-ex-checkout');
Route::get('/wizard/ex-property-listing', $controller_path . '\wizard_example\PropertyListing@index')->name('wizard-ex-property-listing');
Route::get('/wizard/ex-create-deal', $controller_path . '\wizard_example\CreateDeal@index')->name('wizard-ex-create-deal');

// modal
Route::get('/modal-examples', $controller_path . '\modal\ModalExample@index')->name('modal-examples');

// cards
Route::get('/cards/basic', $controller_path . '\cards\CardBasic@index')->name('cards-basic');
Route::get('/cards/advance', $controller_path . '\cards\CardAdvance@index')->name('cards-advance');
Route::get('/cards/statistics', $controller_path . '\cards\CardStatistics@index')->name('cards-statistics');
Route::get('/cards/analytics', $controller_path . '\cards\CardAnalytics@index')->name('cards-analytics');
Route::get('/cards/actions', $controller_path . '\cards\CardActions@index')->name('cards-actions');

// User Interface
Route::get('/ui/accordion', $controller_path . '\user_interface\Accordion@index')->name('ui-accordion');
Route::get('/ui/alerts', $controller_path . '\user_interface\Alerts@index')->name('ui-alerts');
Route::get('/ui/badges', $controller_path . '\user_interface\Badges@index')->name('ui-badges');
Route::get('/ui/buttons', $controller_path . '\user_interface\Buttons@index')->name('ui-buttons');
Route::get('/ui/carousel', $controller_path . '\user_interface\Carousel@index')->name('ui-carousel');
Route::get('/ui/collapse', $controller_path . '\user_interface\Collapse@index')->name('ui-collapse');
Route::get('/ui/dropdowns', $controller_path . '\user_interface\Dropdowns@index')->name('ui-dropdowns');
Route::get('/ui/footer', $controller_path . '\user_interface\Footer@index')->name('ui-footer');
Route::get('/ui/list-groups', $controller_path . '\user_interface\ListGroups@index')->name('ui-list-groups');
Route::get('/ui/modals', $controller_path . '\user_interface\Modals@index')->name('ui-modals');
Route::get('/ui/navbar', $controller_path . '\user_interface\Navbar@index')->name('ui-navbar');
Route::get('/ui/offcanvas', $controller_path . '\user_interface\Offcanvas@index')->name('ui-offcanvas');
Route::get('/ui/pagination-breadcrumbs', $controller_path . '\user_interface\PaginationBreadcrumbs@index')->name('ui-pagination-breadcrumbs');
Route::get('/ui/progress', $controller_path . '\user_interface\Progress@index')->name('ui-progress');
Route::get('/ui/spinners', $controller_path . '\user_interface\Spinners@index')->name('ui-spinners');
Route::get('/ui/tabs-pills', $controller_path . '\user_interface\TabsPills@index')->name('ui-tabs-pills');
Route::get('/ui/toasts', $controller_path . '\user_interface\Toasts@index')->name('ui-toasts');
Route::get('/ui/tooltips-popovers', $controller_path . '\user_interface\TooltipsPopovers@index')->name('ui-tooltips-popovers');
Route::get('/ui/typography', $controller_path . '\user_interface\Typography@index')->name('ui-typography');

// extended ui
Route::get('/extended/ui-avatar', $controller_path . '\extended_ui\Avatar@index')->name('extended-ui-avatar');
Route::get('/extended/ui-blockui', $controller_path . '\extended_ui\BlockUI@index')->name('extended-ui-blockui');
Route::get('/extended/ui-drag-and-drop', $controller_path . '\extended_ui\DragAndDrop@index')->name('extended-ui-drag-and-drop');
Route::get('/extended/ui-media-player', $controller_path . '\extended_ui\MediaPlayer@index')->name('extended-ui-media-player');
Route::get('/extended/ui-perfect-scrollbar', $controller_path . '\extended_ui\PerfectScrollbar@index')->name('extended-ui-perfect-scrollbar');
Route::get('/extended/ui-star-ratings', $controller_path . '\extended_ui\StarRatings@index')->name('extended-ui-star-ratings');
Route::get('/extended/ui-sweetalert2', $controller_path . '\extended_ui\SweetAlert@index')->name('extended-ui-sweetalert2');
Route::get('/extended/ui-text-divider', $controller_path . '\extended_ui\TextDivider@index')->name('extended-ui-text-divider');
Route::get('/extended/ui-timeline-basic', $controller_path . '\extended_ui\TimelineBasic@index')->name('extended-ui-timeline-basic');
Route::get('/extended/ui-timeline-fullscreen', $controller_path . '\extended_ui\TimelineFullscreen@index')->name('extended-ui-timeline-fullscreen');
Route::get('/extended/ui-tour', $controller_path . '\extended_ui\Tour@index')->name('extended-ui-tour');
Route::get('/extended/ui-treeview', $controller_path . '\extended_ui\Treeview@index')->name('extended-ui-treeview');
Route::get('/extended/ui-misc', $controller_path . '\extended_ui\Misc@index')->name('extended-ui-misc');

// icons
Route::get('/icons/tabler', $controller_path . '\icons\Tabler@index')->name('icons-tabler');
Route::get('/icons/font-awesome', $controller_path . '\icons\FontAwesome@index')->name('icons-font-awesome');

// form elements
Route::get('/forms/basic-inputs', $controller_path . '\form_elements\BasicInput@index')->name('forms-basic-inputs');
Route::get('/forms/input-groups', $controller_path . '\form_elements\InputGroups@index')->name('forms-input-groups');
Route::get('/forms/custom-options', $controller_path . '\form_elements\CustomOptions@index')->name('forms-custom-options');
Route::get('/forms/editors', $controller_path . '\form_elements\Editors@index')->name('forms-editors');
Route::get('/forms/file-upload', $controller_path . '\form_elements\FileUpload@index')->name('forms-file-upload');
Route::get('/forms/pickers', $controller_path . '\form_elements\Picker@index')->name('forms-pickers');
Route::get('/forms/selects', $controller_path . '\form_elements\Selects@index')->name('forms-selects');
Route::get('/forms/sliders', $controller_path . '\form_elements\Sliders@index')->name('forms-sliders');
Route::get('/forms/switches', $controller_path . '\form_elements\Switches@index')->name('forms-switches');
Route::get('/forms/extras', $controller_path . '\form_elements\Extras@index')->name('forms-extras');

// form layouts
Route::get('/form/layouts-vertical', $controller_path . '\form_layouts\VerticalForm@index')->name('form-layouts-vertical');
Route::get('/form/layouts-horizontal', $controller_path . '\form_layouts\HorizontalForm@index')->name('form-layouts-horizontal');
Route::get('/form/layouts-sticky', $controller_path . '\form_layouts\StickyActions@index')->name('form-layouts-sticky');

// form wizards
Route::get('/form/wizard-numbered', $controller_path . '\form_wizard\Numbered@index')->name('form-wizard-numbered');
Route::get('/form/wizard-icons', $controller_path . '\form_wizard\Icons@index')->name('form-wizard-icons');
Route::get('/form/validation', $controller_path . '\form_validation\Validation@index')->name('form-validation');

// tables
Route::get('/tables/basic', $controller_path . '\tables\Basic@index')->name('tables-basic');
Route::get('/tables/datatables-basic', $controller_path . '\tables\DatatableBasic@index')->name('tables-datatables-basic');
Route::get('/tables/datatables-advanced', $controller_path . '\tables\DatatableAdvanced@index')->name('tables-datatables-advanced');
Route::get('/tables/datatables-extensions', $controller_path . '\tables\DatatableExtensions@index')->name('tables-datatables-extensions');

// charts
Route::get('/charts/apex', $controller_path . '\charts\ApexCharts@index')->name('charts-apex');
Route::get('/charts/chartjs', $controller_path . '\charts\ChartJs@index')->name('charts-chartjs');

// maps
Route::get('/maps/leaflet', $controller_path . '\maps\Leaflet@index')->name('maps-leaflet');

// laravel example
Route::get('/laravel/user-management', [UserManagement::class, 'UserManagement'])->name('laravel-example-user-management');
Route::resource('/user-list', UserManagement::class);

Route::group(['middleware' => ['web', 'auth.or.agent'],], function () {
$backend_controller_path = 'App\Http\Controllers\Backend';

Route::get('bookings', $backend_controller_path . '\PaxdetailsController@index')->name('paxdetails')->middleware('can:view_booking_lists');
Route::get('bookings/list', $backend_controller_path . '\PaxdetailsController@indexList')->name('paxdetailsList')->middleware('can:view_booking_lists');
Route::get('bookings/queues', $backend_controller_path . '\PaxdetailsController@indexListQueues')->name('paxdetails.queues');
// ->middleware('can:view_booking_queues');
Route::get('bookings/list/render', $backend_controller_path . '\PaxdetailsController@index_data_index')->name('paxdetailsListRender');
Route::get('bookings/indexdata', $backend_controller_path . '\PaxdetailsController@index_data')->name('booking.indexdata');
Route::get('bookings/{id}/show', $backend_controller_path . '\PaxdetailsController@show')->name('booking.show');
Route::post('bookings/update_data', $backend_controller_path . '\PaxdetailsController@update_data')->name('booking.update_data');
Route::post('bookings/update_dataData', $backend_controller_path . '\PaxdetailsController@update_dataData')->name('booking.update_dataData');
Route::get('bookings/fetch/options', $backend_controller_path . '\PaxdetailsController@fetchOptions')->name('booking.fetch.options');
//
Route::post('bookings/UpdateModifyBook', $backend_controller_path . '\PaxdetailsController@UpdateModifyBook')->name('booking.modify_update');
//
Route::post('bookings/booking_log', $backend_controller_path . '\PaxdetailsController@booking_log')->name('booking.booking_log');
Route::get('bookings/{id}/showData', $backend_controller_path . '\PaxdetailsController@showData')->name('booking.showData');
Route::get('bookings/{id}/showData/parent/booking', $backend_controller_path . '\PaxdetailsController@showDataParentBooking')->name('booking.showData.parent.booking');
Route::post('bookings/showData/getTemplate', $backend_controller_path . '\PaxdetailsController@getTemplate')->name('bookings.showData');
Route::post('bookings/showData/getTemplateCanned', $backend_controller_path . '\PaxdetailsController@getTemplateCanned')->name('bookings.showData.canned');
//
Route::post('bookings/whatsappMsg', $backend_controller_path . '\PaxdetailsController@whatsappMessage')->name('booking.whatsappMsg');
Route::post('bookings/whatsappMsg/continue', $backend_controller_path . '\PaxdetailsController@whatsappMessageContinue')->name('booking.whatsappMsg.continue');
Route::post('bookings/whatsappMsg/update', $backend_controller_path . '\PaxdetailsController@whatsappMessageUpdate')->name('booking.whatsappMsg.updatePayment');
//
Route::post('bookings/updatePnr', $backend_controller_path . '\PaxdetailsController@updatePnr')->name('booking.updatePnr');
Route::post('bookings/ticket/update', $backend_controller_path . '\PaxdetailsController@updateTicketNumber')->name('ticket.update');
Route::post('bookings/ticket/pdfsave', $backend_controller_path . '\PaxdetailsController@uploadTicketPdfSave')->name('ticket.upload.ticket.save');
Route::post('bookings/updatePnrAirline', $backend_controller_path . '\PaxdetailsController@updatePnrAirline')->name('booking.updatePnrAirline');
Route::post('bookings/ipdetails', $backend_controller_path . '\PaxdetailsController@userIpdetails')->name('booking.userIpdetails');
// Route::post("$module_name/ipdetails", ['as' => "$module_name.ipdetails", 'uses' => "$controller_name@userIpdetails"]);
Route::get('portalbooking', $backend_controller_path . '\PaxdetailsController@portalindex')->name('portalbooking');
Route::get('modifybooking', $backend_controller_path . '\PaxdetailsController@modifyindex')->name('modifybooking');
Route::get('bookings/queueBooking', $backend_controller_path . '\PaxdetailsController@queueBooking')->name('booking.queueBooking');
Route::post('bookings/indexselecteddelete', $backend_controller_path . '\PaxdetailsController@indexSelectedDelete')->name('booking.indexSelectedDelete');
Route::post('bookings/indexselectedqueue', $backend_controller_path . '\PaxdetailsController@indexSelectedQueue')->name('booking.indexSelectedQueue');
Route::post('bookings/sendTemplateMail', $backend_controller_path . '\PaxdetailsController@sendTemplateMail')->name('booking.sendTemplateMail');
Route::post('bookings/sendComposeTemplateMail', $backend_controller_path . '\PaxdetailsController@sendComposeTemplateMail')->name('booking.sendComposeTemplateMail');
Route::post('bookings/sendTemplateCannedMail', $backend_controller_path . '\PaxdetailsController@sendTemplateCannedMail')->name('booking.sendTemplateCannedMail');
Route::post('bookings/processqueue', $backend_controller_path . '\PaxdetailsController@processQueue')->name('booking.processQueue');
Route::post('paxdetails/remove_cache', $backend_controller_path . '\PaxdetailsController@remove_cache')->name('paxdetails.remove_cache');
Route::post('paxdetails/update_cache', $backend_controller_path . '\PaxdetailsController@update_cache')->name('paxdetails.update_cache');
Route::get('bookingreffilter', $backend_controller_path . '\PaxdetailsController@bookingIdArrGenerator')->name('booking.bookingidarrgenerator');
Route::post('store-ticket', $backend_controller_path . '\PaxdetailsController@store')->name('booking.ticket');
Route::get('bookings/ticket', $backend_controller_path . '\PaxdetailsController@Ticket')->name('backend.bookings.ticket');
// ->middleware('can:view_ticketing_reports');
Route::get('/booking/indexticket', $backend_controller_path . '\PaxdetailsController@indexTicket')->name('backend.bookings.tickets');
Route::get('booking/indextickets', $backend_controller_path . '\PaxdetailsController@indexTicketFilter')->name('backend.bookings.ticketfilter');
Route::get('bookings/new', $backend_controller_path . '\PaxdetailsController@index_new')->name('paxdetails.new');
Route::get('bookings/indexdata/new', $backend_controller_path . '\PaxdetailsController@index_data_new')->name('booking.index_data.new');
Route::get('bookings/queues/new', $backend_controller_path . '\PaxdetailsController@index_queues_new')->name('paxdetails.queues.new')->middleware('can:view_booking_queues_news');
Route::get('bookings/indexdataqueues/new', $backend_controller_path . '\PaxdetailsController@index_data_queues')->name('booking.index_data_queues.new');
Route::get('bookings/indexdatas/filter', $backend_controller_path . '\PaxdetailsController@index_data_filters')->name('booking.index_data_filters.filter');
Route::post('bookings/parentbookingid/update', $backend_controller_path . '\PaxdetailsController@parentBookingIdUpdate')->name('booking.parent.booking.id.update');
Route::get('bookings/index', $backend_controller_path . '\PaxdetailsController@booking_index_view')->name('backend.bookings.bookingindex');
Route::get('bookings/index/view', $backend_controller_path . '\PaxdetailsController@booking_indexs')->name('backend.bookings.bookingindex.view');

Route::get('bookings/beta', $backend_controller_path . '\PaxdetailsController@index_beta')->name('bookings.beta');
Route::get('bookings/beta/data', $backend_controller_path . '\PaxdetailsController@index_beta_data')->name('bookings.beta.data');
// PaxdetailsMoveController
Route::get('paymentgateway/list', $backend_controller_path . '\PaymentGatewayController@index')->name('paymentgatewaylist') ->middleware('can:view_payment_geteway_details');
Route::get('paymentgateway/indexdata/new', $backend_controller_path . '\PaymentGatewayController@indexData')->name('paymentgateway.index_data.new');
Route::get('paymentgateway/show', $backend_controller_path . '\PaymentGatewayController@show')->name('paymentgateway.show');


Route::get('movedata', $backend_controller_path . '\PaxdetailsMoveController@moveData')->name('movedata');
Route::post('booking/itineraryFormat', $backend_controller_path . '\PaxdetailsController@itineraryFormat')->name('backend.bookings.itineraryFormat');
Route::post('booking/bookingRedirect', $backend_controller_path . '\PaxdetailsController@bookingRedirect')->name('backend.bookings.redirect');
Route::get('booking/paylink/response', $backend_controller_path . '\PaxdetailsController@paylinkResponseData')->name('booking.paylinkResponseData');

Route::post('booking/eticket/retrieve',$backend_controller_path . '\EticketRetrieveController@index')->name('backend.bookings.eticket.retrieve');
Route::post('send/eticket/mail', $backend_controller_path . '\PaxdetailsController@sendEticketMail')->name('backend.send.eticketmail');
Route::post('external/pnr/store', $backend_controller_path . '\BookingUpdatePnrDataStoreController@ExternalPnrStore')->name('backend.external.pnr.store');
Route::post('bookings/test/booking-data/{id}', $backend_controller_path . '\PaxdetailsController@testBooking')->name('bookings.test-booking');
});
$backend_controller_path = 'App\Http\Controllers\Backend';

Route::middleware(['auth', 'check.permission:view_booking_queues_betas'])->group(function () use ($backend_controller_path) {
    Route::get('bookings/index/queues', $backend_controller_path . '\BookingQueueController@index_queues')->name('booking.index.queue');
    Route::get('bookings/queues/data', $backend_controller_path . '\BookingQueueController@index_data_queues')->name('booking.index.queue.data');

});
$backend_controller_path = 'App\Http\Controllers\Backend';

Route::middleware(['auth.or.agent', 'check.permission:view_booking_list'])->group(function () use ($backend_controller_path) {
    Route::get('bookings/pax/list', $backend_controller_path . '\PaxDetailsViewController@index')->name('bookings.pax.list');
    Route::get('bookings/pax/view', $backend_controller_path . '\PaxDetailsViewController@index_data')->name('bookings.pax.view');
    Route::get('bookings/autocompleteagentEmail', $backend_controller_path . '\PaxDetailsViewController@autocompleteAgentEmail')->name('agent.email.autocomplete');
    Route::get('bookings/autocompleteagencyName', $backend_controller_path . '\PaxDetailsViewController@autocompleteAgencyName')->name('agency.name.autocomplete');

});
Route::group(['namespace' => 'App\Http\Controllers\Backend', 'as' => 'backend.', 'middleware' => ['web', 'auth'], 'prefix' => 'admin'], function () {

    $module_name = 'payment_page';
    $controller_name = 'PaymentPageController';
    Route::get("$module_name/index", ['as' => "$module_name.index", 'uses' => "$controller_name@index"]);
    Route::post("$module_name/confirmstore", ['as' => "$module_name.confirmstore", 'uses' => "$controller_name@confirmStore"]);
    Route::post("$module_name/paymentstore", ['as' => "$module_name.paymentstore", 'uses' => "$controller_name@paymentStore"]);
    // Route::post("$module_name/paymentstoreData", ['as' => "$module_name.paymentstoreData", 'uses' => "$controller_name@paymentStoreData"]);
    Route::match(['get', 'post'],"$module_name/paymentstoreData", ['as' => "$module_name.paymentstoreData", 'uses' => "$controller_name@paymentStoreData"]);

});




Route::group(['namespace' => 'App\Http\Controllers\Backend', 'as' => 'backend.', 'middleware' => ['web', 'auth'], 'prefix' => 'admin'], function () {
    $module_name = 'users';
    $controller_name = 'UserController';
    Route::get("$module_name/profile/{id}", ['as' => "$module_name.profile", 'uses' => "$controller_name@profile"]);
    Route::get("$module_name/profile/{id}/edit", ['as' => "$module_name.profileEdit", 'uses' => "$controller_name@profileEdit"]);
    Route::patch("$module_name/profile/{id}/edit", ['as' => "$module_name.profileUpdate", 'uses' => "$controller_name@profileUpdate"]);
    Route::get("$module_name/emailConfirmationResend/{id}", ['as' => "$module_name.emailConfirmationResend", 'uses' => "$controller_name@emailConfirmationResend"]);
    Route::delete("$module_name/userProviderDestroy", ['as' => "$module_name.userProviderDestroy", 'uses' => "$controller_name@userProviderDestroy"]);
    Route::get("$module_name/profile/changeProfilePassword/{id}", ['as' => "$module_name.changeProfilePassword", 'uses' => "$controller_name@changeProfilePassword"]);
    Route::patch("$module_name/profile/changeProfilePassword/{id}", ['as' => "$module_name.changeProfilePasswordUpdate", 'uses' => "$controller_name@changeProfilePasswordUpdate"]);
    Route::get("$module_name/changePassword/{id}", ['as' => "$module_name.changePassword", 'uses' => "$controller_name@changePassword"]);
    Route::patch("$module_name/changePassword/{id}", ['as' => "$module_name.changePasswordUpdate", 'uses' => "$controller_name@changePasswordUpdate"]);
    Route::match(['patch', 'get'], "$module_name/trashed", [
        'as' => "$module_name.trashed",
        'uses' => "$controller_name@trashed",
    ]);
        Route::match(['patch', 'get'],"$module_name/trashed/{id}", ['as' => "$module_name.restore", 'uses' => "$controller_name@restore"]);
    Route::get("$module_name/index_data", ['as' => "$module_name.index_data", 'uses' => "$controller_name@index_data"]);
    Route::get("$module_name/index_list", ['as' => "$module_name.index_list", 'uses' => "$controller_name@index_list"]);
    // Route::resource("$module_name", "$controller_name");
    Route::get('users', [UserController::class, 'index'])->name('users.index');
    // ->middleware('can:view_users');
    Route::get('users/create', [UserController::class, 'create'])->name('users.create');
    Route::post('users', [UserController::class, 'store'])->name('users.store');
    Route::get('users/{id}', [UserController::class, 'show'])->name('users.show')->middleware('can:view_user_details');
    Route::get('users/{user}/edit', [UserController::class, 'edit'])->name('users.edit')->middleware('can:view_user_edit');
    Route::patch('users/{user}', [UserController::class, 'update'])->name('users.update');
    Route::delete('users/{user}', [UserController::class, 'destroy'])->name('users.destroy');
    Route::match(['patch', 'get'],"$module_name/{id}/block", ['as' => "$module_name.block", 'uses' => "$controller_name@block", 'middleware' => ['permission:block_users']]);
    Route::match(['patch', 'get'],"$module_name/{id}/unblock", ['as' => "$module_name.unblock", 'uses' => "$controller_name@unblock", 'middleware' => ['permission:block_users']]);

    Route::middleware(['auth', 'check.permission:view_roles'])->group(function () use ($module_name, $controller_name) {

    $module_name = 'roles';
    $controller_name = 'RolesController';
    Route::resource("$module_name", "$controller_name");

    });
	Route::post("suspect/updatedata",['as' => "suspect.updatedata", 'uses' => "SuspectController@updatedata"]);
    Route::get("suspect/index",['as' => "suspect.index", 'uses' => "SuspectController@index"]) ->middleware('can:view_suspect');



});

Route::group(['namespace' => 'App\Http\Controllers\Backend', 'as' => 'backend.', 'middleware' => ['web', 'auth','check.permission:view_feerule'], 'prefix' => 'admin'], function () {
    $module_name = 'gflightsfeerule';
    $controller_name = 'GflightsfeeruleController';

    Route::get("$module_name/getcarrier", ['as' => "$module_name.getcarrier", 'uses' => "$controller_name@getcarrier"]);

    Route::get("$module_name/index", ['as' => "$module_name.index", 'uses' => "$controller_name@index"]);
    Route::get("$module_name/uploadindex", ['as' => "$module_name.uploadindex", 'uses' => "$controller_name@uploadindex"]);
    Route::post("$module_name/indexrulestore", ['as' => "$module_name.indexRuleStore", 'uses' => "$controller_name@indexRuleStore"]);

    Route::get("$module_name/create", ['as' => "$module_name.create", 'uses' => "$controller_name@create"]);
    Route::post("$module_name/store", ['as' => "$module_name.store", 'uses' => "$controller_name@store"]);
    Route::get("$module_name/edit/{id}", ['as' => "$module_name.edit", 'uses' => "$controller_name@edit"]);
    Route::post("$module_name/update/{id}", ['as' => "$module_name.update", 'uses' => "$controller_name@update"]);
    Route::get("$module_name/copy/{id}", ['as' => "$module_name.copy", 'uses' => "$controller_name@copy"]);
    Route::post("$module_name/copyupdate/{id}", ['as' => "$module_name.copyupdate", 'uses' => "$controller_name@copyupdate"]);

    Route::post("$module_name/importxml", ['as' => "$module_name.importxml", 'uses' => "$controller_name@importxml"]);
    Route::post("$module_name/updateDatabase", ['as' => "$module_name.updateDatabase", 'uses' => "$controller_name@updateDatabase"]);

    Route::get("$module_name/xmlgenerate/{source?}", ['as' => "$module_name.xmlgenerate", 'uses' => "$controller_name@xmlgenerate"]);

    Route::get("$module_name/xmlgeneratedownload/{source?}", ['as' => "$module_name.xmlgeneratedownload", 'uses' => "$controller_name@xmlgeneratedownload"]);
    Route::post("$module_name/uploadxmlsftp/{source?}", ['as' => "$module_name.uploadxmlsftp", 'uses' => "$controller_name@uploadxmlsftp"]);
    Route::delete("$module_name/destroy/{id}", ['as' => "$module_name.destroy", 'uses' => "$controller_name@destroy"]);
    Route::get("$module_name/createform", ['as' => "$module_name.createform", 'uses' => "$controller_name@createForm"]);
    Route::get("$module_name/editform", ['as' => "$module_name.editform", 'uses' => "$controller_name@editForm"]);


    Route::get("gflightsfeerule/originairport", ['as' => "$module_name.originAirport", 'uses' => "$controller_name@originAirport"]);

    Route::get("gflightsfeerule/origincountry", ['as' => "$module_name.originCountry", 'uses' => "$controller_name@originCountry"]);

    // Route::get("gflightsfeerule/destinationairport", ['as' => "$module_name.destinationairport", 'uses' => "$controller_name@destinationAirport"]);

    // Route::get("gflightsfeerule/destinationcountry", ['as' => "$module_name.destinationcountry", 'uses' => "$controller_name@destinationCountry"]);
    Route::get("gflightsfeerule/updateRules/{source?}", ['as' => "gflightsfeerule.updateRules", 'uses' => "$controller_name@updateRules"]);
    Route::get("gflightsfeerule/linkEnableActiveMins", ['as' => "gflightsfeerule.linkEnableActiveMins", 'uses' => "$controller_name@linkEnableActiveMins"]);
    Route::get("gflightsfeerule/checkMinsValue", ['as' => "gflightsfeerule.checkMinsValue", 'uses' => "$controller_name@checkMinsValue"]);
    Route::post("gflightsfeerule/updateActivationtime",['as'=>"gflightsfeerule.updateActivationtime", 'uses' => "$controller_name@activationTime"]);
    Route::get("gflightsfeerule/feeRuleIsActiveFileGet",['as'=>"gflightsfeerule.feeRuleIsActiveFileGet", 'uses' => "$controller_name@feeRuleIsActiveFileGet"]);
    Route::post("gflightsfeerule/updatemarkup/{id}", ['as' => "gflightsfeerule.updatemarkup", 'uses' => "$controller_name@updatemarkup"]);


    Route::middleware('web')->get('/back', function () {
        return back();
    })->name('back');


});
Route::get('admin/gflightsfeerule/uploadindexfilter',[GflightsfeeruleController::class,'uploadindexFilter'])->name('backend.gflightsfeerule.uploadindexFilter');

Route::group(['namespace' => 'App\Http\Controllers\Backend', 'as' => 'backend.', 'middleware' => ['web', 'auth','check.permission:view_feerule_group'], 'prefix' => 'admin'], function () {
    $module_name = 'feerulegroup';
    $controller_name = 'FeeruleGroupController';

    Route::get("$module_name/index/{source?}", ['as' => "$module_name.index", 'uses' => "$controller_name@index"]);
    Route::get("$module_name/indexs/{source?}", ['as' => "$module_name.indexs", 'uses' => "$controller_name@index"]);
    Route::get("$module_name/feerulegroupedit/{id}/{parameter?}", ['as' => "$module_name.feerulegroupedit", 'uses' => "$controller_name@feerulegroupedit"]);    Route::get("$module_name/feerulegroupdelete/{id}", ['as' => "$module_name.feerulegroupdelete", 'uses' => "$controller_name@feerulegroupdelete"]);
    Route::post("$module_name/feerulegroupcreate", ['as' => "$module_name.feerulegroupcreate", 'uses' => "$controller_name@feerulegroupcreate"]);
    Route::post("$module_name/feerulegroupupdate/{id}", ['as' => "$module_name.feerulegroupupdate", 'uses' => "$controller_name@feerulegroupupdate"]);
    Route::get("$module_name/sortdatatables/{id}", ['as' => "$module_name.sortdatatable", 'uses' => "$controller_name@sortDatatable"]);
    Route::get("$module_name/feerulegroupcopy/{id}", ['as' => "$module_name.feerulegroupcopy", 'uses' => "$controller_name@feerulegroupcopy"]);
    Route::get("$module_name/feeruleEditInGroup/{id}", ['as' => "$module_name.feeruleEditInGroup", 'uses' => "$controller_name@feeruleEditInGroup"]);
    Route::get("$module_name/feerulecreateform/{id}", ['as' => "$module_name.feerulecreateform", 'uses' => "$controller_name@feerulecreateform"]);
    Route::post("$module_name/store", ['as' => "$module_name.store", 'uses' => "$controller_name@store"]);
    Route::post("$module_name/update/{id}", ['as' => "$module_name.update", 'uses' => "$controller_name@update"]);

    Route::middleware('web')->get('/back', function () {
        return back();
    })->name('back');




});

Route::get('admin/feerulegroup/filterindex',[FeeruleGroupController::class,'Filterindex'])->name('backend.feerulegroup.filterindex');
Route::post('/check-priority', [FeeruleGroupController::class, 'checkPriority'])->name('check.priority');
Route::post('/check-agent_group_id', [B2bAgentGroupController::class, 'checkAgentGroupId'])->name('check.agent_group_id');

Route::group(['namespace' => 'App\Http\Controllers\Backend', 'as' => 'backend.', 'middleware' => ['web', 'auth','check.permission:view_rule_template'], 'prefix' => 'admin'], function () {
    $module_name = 'templates';
    $controller_name = 'TemplatesController';
    Route::get("$module_name/index/{source?}", ['as' => "$module_name.index", 'uses' => "$controller_name@index"]);
    Route::get("$module_name/indexs/{source?}", ['as' => "$module_name.indexs", 'uses' => "$controller_name@index"]);

   // Create template
   Route::get("$module_name/create", ['as' => "$module_name.create", 'uses' => "$controller_name@create"]);
   Route::post("$module_name/store", ['as' => "$module_name.store", 'uses' => "$controller_name@store"]);

   //Route::get('templates/create', 'Backend\TemplatesController@create')->name('templates.create');
   //Route::post('templates', 'Backend\TemplatesController@store')->name('templates.store');

   // Edit template
   Route::get("$module_name/edit/{id}/{source?}", ['as' => "$module_name.edit", 'uses' => "$controller_name@edit"]);
   Route::post("$module_name/update/{id}/{source?}", ['as' => "$module_name.update", 'uses' => "$controller_name@update"]);

   //Route::get('templates/{id}/edit', 'Backend\TemplatesController@edit')->name('templates.edit');
   //Route::put('templates/{id}', 'Backend\TemplatesController@update')->name('templates.update');
   Route::post("$module_name/updateDatabase", ['as' => "$module_name.updateDatabase", 'uses' => "$controller_name@updateDatabase"]);

   // Delete template
   //Route::delete('templates/{id}', 'Backend\TemplatesController@destroy')->name('templates.destroy');
   Route::delete("$module_name/destroy/{id}", ['as' => "$module_name.destroy", 'uses' => "$controller_name@destroy"]);

   });


   // gflightlocation
   $backend_controller_path = 'App\Http\Controllers\Backend';

   Route::middleware(['auth', 'check.permission:view_location_group'])->group(function () use ($backend_controller_path) {

Route::get('admin/gflightslocation/index', $backend_controller_path . '\GflightsLocationController@index')->name('backend.gflightslocation.index');
Route::get('admin/gflightslocation/create', $backend_controller_path . '\GflightsLocationController@create')->name('backend.gflightslocation.create');
Route::get('admin/gflightslocation/city', $backend_controller_path . '\GflightsLocationController@get_city')->name('backend.gflightslocation.city');
Route::get('admin/gflightslocation/country', $backend_controller_path . '\GflightsLocationController@get_country')->name('backend.gflightslocation.country');
Route::get('admin/gflightslocation/airport', $backend_controller_path . '\GflightsLocationController@get_airport')->name('backend.gflightslocation.airport');
Route::post('admin/gflightslocation/store', $backend_controller_path . '\GflightsLocationController@store')->name('backend.gflightslocation.store');
Route::any('admin/gflightslocation/edit/{id}', $backend_controller_path . '\GflightsLocationController@edit')->name('backend.gflightslocation.edit');
Route::post('admin/gflightslocation/update/{id}', $backend_controller_path . '\GflightsLocationController@update')->name('backend.gflightslocation.update');
Route::delete('admin/gflightslocation/destroy/{id}', $backend_controller_path . '\GflightsLocationController@destroy')->name('backend.gflightslocation.destroy');

   });

// carriers
Route::middleware(['auth', 'check.permission:view_carrier_group'])->group(function () use ($backend_controller_path) {
    Route::get('admin/carriers/index', $backend_controller_path . '\CarrierController@index')->name('backend.carriers.index');
Route::get('admin/carriers/create', $backend_controller_path . '\CarrierController@create')->name('backend.carriers.create');
Route::get('admin/carriers/edit/{id}', $backend_controller_path . '\CarrierController@edit')->name('backend.carriers.edit');
Route::get('admin/carriers/store', $backend_controller_path . '\CarrierController@store')->name('backend.carriers.store');
Route::put('admin/carriers/{id}/update', $backend_controller_path . '\CarrierController@update')->name('backend.carriers.update');
Route::delete('admin/carriers/{id}/destroy', $backend_controller_path . '\CarrierController@destroy')->middleware('web') ->name('backend.carriers.destroy');
Route::match(['get', 'put', 'delete'], 'admin/carriers/airline', $backend_controller_path . '\CarrierController@get_airlines')->name('backend.carriers.airline');
});









// portalfeerule
Route::middleware(['auth', 'check.permission:view_portalfeerules'])->group(function () use ($backend_controller_path) {

$backend_controller_path = 'App\Http\Controllers\Backend';

Route::get('admin/portalfeerule/index', $backend_controller_path . '\PortalFeeruleController@index')->name('backend.portalfeerule.index');
Route::get('admin/portalfeerule/create', $backend_controller_path . '\PortalFeeruleController@create')->name('backend.portalfeerule.create');
Route::get('admin/portalfeerule/xmlgenerate', $backend_controller_path . '\PortalFeeruleController@xmlgenerate')->name('backend.portalfeerule.xmlgenerate');
Route::get('admin/portalfeerule/xmlgeneratedownload', $backend_controller_path . '\PortalFeeruleController@xmlgeneratedownload')->name('backend.portalfeerule.xmlgeneratedownload');
Route::match(['get', 'post'],'admin/portalfeerule/uploadxmlsftp', $backend_controller_path . '\PortalFeeruleController@uploadxmlsftp')->name('backend.portalfeerule.uploadxmlsftp');
Route::post('admin/portalfeerule/updateDatabase', $backend_controller_path . '\PortalFeeruleController@updateDatabase')->name('backend.portalfeerule.updateDatabase');
Route::post('admin/portalfeerule/update/{id}', $backend_controller_path . '\PortalFeeruleController@update')->name('backend.portalfeerule.update');
Route::get('admin/portalfeerule/updateRules', $backend_controller_path . '\PortalFeeruleController@updateRules')->name('backend.portalfeerule.updateRules');
Route::get('admin/portalfeerule/originAirport', $backend_controller_path . '\PortalFeeruleController@originAirport')->name('backend.portalfeerule.originAirport');
Route::get('admin/portalfeerule/originCountry', $backend_controller_path . '\PortalFeeruleController@originCountry')->name('backend.portalfeerule.originCountry');
Route::match(['get', 'post'],'admin/portalfeerule/store', $backend_controller_path . '\PortalFeeruleController@store')->name('backend.portalfeerule.store');
Route::get('admin/portalfeerule/createform', $backend_controller_path . '\PortalFeeruleController@createForm')->name('backend.portalfeerule.createform');
Route::get('admin/portalfeerule/uploadindex', $backend_controller_path . '\PortalFeeruleController@uploadindex')->name('backend.portalfeerule.uploadindex');
Route::get('admin/portalfeerule/{id}/edit', $backend_controller_path . '\PortalFeeruleController@edit')->name('backend.portalfeerule.edit');
Route::get('admin/portalfeerule/copy/{id}', $backend_controller_path . '\PortalFeeruleController@copy')->name('backend.portalfeerule.copy');
Route::match(['get', 'post'],'admin/portalfeerule/{id}/copyupdate', $backend_controller_path . '\PortalFeeruleController@copyupdate')->name('backend.portalfeerule.copyupdate');
Route::delete('admin/portalfeerule/{id}', $backend_controller_path . '\PortalFeeruleController@destroy')->name('backend.portalfeerule.destroy');
Route::get('admin/portalfeerule/editform', $backend_controller_path . '\PortalFeeruleController@editForm')->name('backend.portalfeerule.editform');
Route::post('admin/portalfeerule/updatemarkup/{id}', $backend_controller_path . '\PortalFeeruleController@updatemarkup')->name('backend.portalfeerule.updatemarkup');
Route::get('admin/portalfeerule/linkEnableActiveMins', $backend_controller_path . '\PortalFeeruleController@linkEnableActiveMins')->name('backend.portalfeerule.linkEnableActiveMins');
Route::get('admin/portalfeerule/checkMinsValue', $backend_controller_path . '\PortalFeeruleController@checkMinsValue')->name('backend.portalfeerule.checkMinsValue');
Route::post('admin/portalfeerule/updateActivationtime', $backend_controller_path . '\PortalFeeruleController@updateActivationtime')->name('backend.portalfeerule.updateActivationtime');
Route::get('admin/portalfeerule/feeRuleIsActiveFileGet', $backend_controller_path . '\PortalFeeruleController@feeRuleIsActiveFileGet')->name('backend.portalfeerule.feeRuleIsActiveFileGet');
Route::post('admin/portalfeerule/indexrulestore', $backend_controller_path . '\PortalFeeruleController@indexrulestore')->name('backend.portalfeerule.indexrulestore');
});


// portalfeeruleGroup
Route::middleware(['auth', 'check.permission:view_portalfeerulgroups'])->group(function () use ($backend_controller_path) {

$backend_controller_path = 'App\Http\Controllers\Backend';
Route::get('admin/portalfeerulegroup/index', $backend_controller_path . '\PortalFeeruleGroupController@index')->name('backend.portalfeerulgroup.index');
Route::post('admin/portalfeerulegroup/create', $backend_controller_path . '\PortalFeeruleGroupController@create')->name('backend.portalfeerulgroup.create');
Route::get('admin/portalfeerulegroup/feerulegroupedit/{id}/{parameter?}', $backend_controller_path . '\PortalFeeruleGroupController@feerulegroupedit')->name('backend.portalfeerulgroup.feerulegroupedit');
Route::get('admin/portalfeerulegroup/{id}/delete', $backend_controller_path . '\PortalFeeruleGroupController@feerulegroupdelete')->name('backend.portalfeerulgroup.feerulegroupdelete');
Route::get('admin/portalfeerulegroup/{id}', $backend_controller_path . '\PortalFeeruleGroupController@feeruleEditInGroup')->name('backend.portalfeerulgroup.feeruleEditInGroup');
Route::post('admin/portalfeerulegroup/store', $backend_controller_path . '\PortalFeeruleGroupController@store')->name('backend.portalfeerulgroup.store');
Route::put('admin/portalfeerulegroup/{id}', $backend_controller_path . '\PortalFeeruleGroupController@update')->name('backend.portalfeerulgroup.update');
Route::post('admin/portalfeerulegroup/feerulegroupupdate/{id}', $backend_controller_path . '\PortalFeeruleGroupController@feerulegroupupdate')->name('backend.portalfeerulgroup.feerulegroupupdate');
Route::post('admin/portalfeerulegroup/updateDatabase', $backend_controller_path . '\PortalFeeruleGroupController@updateDatabase')->name('backend.portalfeerulgroup.updateDatabase');
Route::get('admin/portalfeerulegroup/originAirport', $backend_controller_path . '\PortalFeeruleGroupController@originAirport')->name('backend.portalfeerulgroup.originAirport');
Route::get('admin/portalfeerulegroup/sortdatatable/{id}', $backend_controller_path . '\PortalFeeruleGroupController@sortDatatable')->name('backend.portalfeerulgroup.sortdatatable');
Route::get('admin/portalfeerulegroup/feerulecreateform/{id}', $backend_controller_path . '\PortalFeeruleGroupController@feeruleCreateForm')->name('backend.portalfeerulgroup.feerulecreateform');
Route::get('admin/portalfeerulegroup/feerulegroupcopy/{id}', $backend_controller_path . '\PortalFeeruleGroupController@feerulegroupcopy')->name('backend.portalfeerulgroup.feerulegroupcopy');
Route::get('admin/portalfeerulegroup/originCountry', $backend_controller_path . '\PortalFeeruleGroupController@originCountry')->name('backend.portalfeerulgroup.originCountry');
Route::match(['get', 'post'], 'admin/portalfeerulegroup/feerulegroupcreate', $backend_controller_path . '\PortalFeeruleGroupController@feerulegroupcreate')->name('backend.portalfeerulgroup.feerulegroupcreate');

});




// portalcarriers

Route::middleware(['auth', 'check.permission:view_portalcarriers'])->group(function () use ($backend_controller_path) {
    $backend_controller_path = 'App\Http\Controllers\Backend';
Route::get('admin/portalcarriers/index', $backend_controller_path . '\PortalCarriersController@index')->name('backend.portalcarriers.index');
Route::get('admin/portalcarriers/create', $backend_controller_path . '\PortalCarriersController@create')->name('backend.portalcarriers.create');
Route::get('admin/portalcarriers/edit/{id}', $backend_controller_path . '\PortalCarriersController@edit')->name('backend.portalcarriers.edit');
Route::get('admin/portalcarriers/store', $backend_controller_path . '\PortalCarriersController@store')->name('backend.portalcarriers.store');
Route::put('admin/portalcarriers/{id}/update', $backend_controller_path . '\PortalCarriersController@update')->name('backend.portalcarriers.update');
Route::delete('admin/portalcarriers/{id}/destroy', $backend_controller_path . '\PortalCarriersController@destroy')->name('backend.portalcarriers.destroy');
Route::match(['get', 'put', 'delete'], 'admin/portalcarriers/airline', $backend_controller_path . '\PortalCarriersController@get_airlines')->name('backend.portalcarriers.airline');
});








// portaltemplatesrole
Route::middleware(['auth', 'check.permission:view_portaltemplatesrules'])->group(function () use ($backend_controller_path) {

$backend_controller_path = 'App\Http\Controllers\Backend';
Route::get('admin/portaltemplatesrule/index', $backend_controller_path . '\PortalTemplateController@index')->name('backend.portaltemplatesrule.index');
Route::post('admin/portaltemplatesrule/updateDatabase', $backend_controller_path . '\PortalTemplateController@updateDatabase')->name('backend.portaltemplatesrule.updateDatabase');
Route::get('admin/portaltemplatesrule/create', $backend_controller_path . '\PortalTemplateController@create')->name('backend.portaltemplatesrule.create');
Route::match(['get', 'post'],'admin/portaltemplatesrule/store', $backend_controller_path . '\PortalTemplateController@store')->name('backend.portaltemplatesrule.store');
Route::get('admin/portaltemplatesrule/{id}/edit', $backend_controller_path . '\PortalTemplateController@edit')->name('backend.portaltemplatesrule.edit');
Route::delete('admin/portaltemplatesrule/{id}/destroy', $backend_controller_path . '\PortalTemplateController@destroy')->name('backend.portaltemplatesrule.destroy');
Route::match(['get', 'post'],'admin/portaltemplatesrule/update/{id}', $backend_controller_path . '\PortalTemplateController@update')->name('backend.portaltemplatesrule.update');

});



// portalgflightlocation
Route::middleware(['auth', 'check.permission:view_portalgflightlocations'])->group(function () use ($backend_controller_path) {

$backend_controller_path = 'App\Http\Controllers\Backend';
Route::get('admin/portalgflightlocation/index', $backend_controller_path . '\PortalGflightGroupController@index')->name('backend.portalgflightlocation.index');
Route::get('admin/portalgflightlocation/create', $backend_controller_path . '\PortalGflightGroupController@create')->name('backend.portalgflightlocation.create');
Route::post('admin/portalgflightlocation/store', $backend_controller_path . '\PortalGflightGroupController@store')->name('backend.portalgflightlocation.store');
Route::get('admin/portalgflightlocation/{id}/edit', $backend_controller_path . '\PortalGflightGroupController@edit')->name('backend.portalgflightlocation.edit');
Route::delete('admin/portalgflightlocation/{id}', $backend_controller_path . '\PortalGflightGroupController@destroy')->name('backend.portalgflightlocation.destroy');
Route::get('admin/portalgflightlocation/city', $backend_controller_path . '\PortalGflightGroupController@get_city')->name('backend.portalgflightlocation.city');
Route::get('admin/portalgflightlocation/country', $backend_controller_path . '\PortalGflightGroupController@get_country')->name('backend.portalgflightlocation.country');
Route::get('admin/portalgflightlocation/airport', $backend_controller_path . '\PortalGflightGroupController@get_airport')->name('backend.portalgflightlocation.airport');
Route::post('admin/portalgflightlocation/update/{id}', $backend_controller_path . '\PortalGflightGroupController@update')->name('backend.portalgflightlocation.update');
});
// paymentpage in frontend
$front_end_controller_path = 'App\Http\Controllers\Frontend';
Route::get('/confirmfare', $front_end_controller_path . '\PaymentPageFront@confirmfare')->name('confirmfare');
Route::get('/payment', $front_end_controller_path . '\PaymentPageFront@index')->name('payment');
Route::get('/statusupdate', $front_end_controller_path . '\PaymentPageFront@statusUpdate')->name('statusupdate');
Route::get('/paymentbillstore', $front_end_controller_path . '\PaymentPageFront@paymentbillstore')->name('paymentbillstore');

//airlines block
Route::get('airlineblock/index', $backend_controller_path . '\AirlineBlockController@index')->name('airlineblock.index');
Route::get('airlineblock/create', $backend_controller_path . '\AirlineBlockController@create')->name('airlineblock.create');
Route::post('airlineblock/store', $backend_controller_path . '\AirlineBlockController@store')->name('airlineblock.store');
Route::delete('airlineblock/delete/{airlineBlock}', $backend_controller_path . '\AirlineBlockController@airlineBlockDelete')->name('airlineblock.delete');

//portal config
Route::get('portalconfig/index', $backend_controller_path .'\PortalConfigController@index')->name('portalconfig.index');
Route::post('portalconfig/fetchPortalConfig', $backend_controller_path .'\PortalConfigController@fetchPortalConfig')->name('portalconfig.fetchPortalConfig');
Route::post('portalconfig/basicDetailsUpdate', $backend_controller_path .'\PortalConfigController@basicDetailsUpdate')->name('portalconfig.basicDetailsUpdate');
Route::post('portalconfig/languagesUpdate', $backend_controller_path .'\PortalConfigController@languagesUpdate')->name('portalconfig.languagesUpdate');
Route::post('portalconfig/endpointsUpdate', $backend_controller_path .'\PortalConfigController@endpointsUpdate')->name('portalconfig.endpointsUpdate');
Route::post('portalconfig/sectionContentUpdate', $backend_controller_path .'\PortalConfigController@sectionContentUpdate')->name('portalconfig.sectionContentUpdate');

Route::post('portalconfig/sectionContentCreate', $backend_controller_path .'\PortalConfigController@sectionContentCreate')->name('portalconfig.sectionContentCreate');
Route::post('portalconfig/endpointsCreate', $backend_controller_path .'\PortalConfigController@endpointsCreate')->name('portalconfig.endpointsCreate');
Route::post('portalconfig/languagesCreate', $backend_controller_path .'\PortalConfigController@languagesCreate')->name('portalconfig.languagesCreate');
Route::post('portalconfig/basicDetailsCreate', $backend_controller_path .'\PortalConfigController@basicDetailsCreate')->name('portalconfig.basicDetailsCreate');


// bookingblock
Route::middleware(['auth', 'check.permission:view_booking_block'])->group(function () use ($backend_controller_path) {
Route::get('bookingblock/index', $backend_controller_path . '\BookingBlockController@index')->name('bookingblock.index');
Route::post('bookingblock/updatedata', $backend_controller_path . '\BookingBlockController@updatedata')->name('bookingblock.updatedata');
});

Route::any('/already_respond', function()
{
    return view('frontend.flights.paymentpage.already_respond');
});
$backend_controller_path = 'App\Http\Controllers\Backend';

Route::middleware(['auth.or.agent', 'check.permission:view_flights'])->group(function () use ($backend_controller_path) {
    Route::get('flights', $backend_controller_path. '\FlightController@index')->name('backend.flights.flights');
    Route::post('flights/link', $backend_controller_path. '\FlightController@domainName')->name('backend.flights.link');
});


// chat menu

Route::group(['middleware' => ['web', 'auth']], function () use ($backend_controller_path) {
    // Route::view('flights', 'backend.flights.flights');
    Route::get('chat/app', $backend_controller_path. '\AppChatController@index')->name('backend.chat');
    Route::post('chat/app/history', $backend_controller_path. '\AppChatController@getChatHistory')->name('backend.chat.app');
    Route::post('chat/app/history/loader', $backend_controller_path. '\AppChatController@getChatHistoryLoader')->name('backend.chat.app.loader');
    Route::post('/chat/send/message', $backend_controller_path. '\AppChatController@chatSendMessage')->name('backend.chat.send.message');
    Route::get('whatsapp/support', $backend_controller_path. '\AppChatController@whatsappSupport')->name('backend.whatsapp.support');
});

// Email menu

Route::middleware(['auth', 'check.permission:view_emails'])->group(function () use ($backend_controller_path) {
    Route::get('email/app/{fullScreen?}', $backend_controller_path. '\AppEmailController@index')->name('backend.email');
    Route::post('email/app/history', $backend_controller_path. '\AppEmailController@getEmailHistory')->name('backend.email.app');
    Route::post('email/app/isread/update', $backend_controller_path. '\AppEmailController@getEmailIsReadUpdate')->name('backend.email.isread.update');
    Route::post('email/send-mail-outbound', $backend_controller_path. '\AppEmailController@sendEmailOutbound')->name('backend.email.send.mail');
    Route::post('email/assign-agent-notes', $backend_controller_path. '\AppEmailController@assignAgentNotes')->name('backend.assign.agent.notes');
    Route::post('email/internal-notes', $backend_controller_path. '\AppEmailController@internalNotesEmail')->name('backend.internal.notes');
    Route::post('email/booking/id/update', $backend_controller_path. '\AppEmailController@conversationBookingIdUpdate')->name('backend.booking.update');
    Route::get('email/notification', $backend_controller_path. '\AppEmailController@mailNotification')->name('mail.notification');
    Route::get('/booking/notification', $backend_controller_path. '\AppEmailController@bookingNotification')->name('booking.notification');
    Route::post('email/transactionMenu', $backend_controller_path. '\AppEmailController@transactionMenu')->name('booking.email.transactionMenu');
    Route::get('email/get-email-list', $backend_controller_path. '\AppEmailController@getEmailList')->name('backend.email.list.get');
    Route::post('email/update/collaborators', $backend_controller_path. '\AppEmailController@updateCollaborators')->name('backend.email.list.collaborators');
    Route::post('email/list-delete', $backend_controller_path. '\AppEmailController@delete')->name('backend.email.list.delete');
    Route::post('email/ticket/id/update', $backend_controller_path. '\AppEmailController@linkTicketIdUpdate')->name('backend.ticket.id.update');
    Route::post('email/ticket/id/view', $backend_controller_path. '\AppEmailController@linkTicketIdView')->name('backend.ticket.id.view');
    Route::post('email/ticket/id/merge', $backend_controller_path. '\AppEmailController@linkTicketIdMerge')->name('backend.ticket.id.merge');


});

// assignedemail
Route::middleware(['auth', 'check.permission:view_assigned_emails'])->group(function () use ($backend_controller_path) {

     Route::get('assignedemail/app', $backend_controller_path. '\AppEmailController@assignedEmail')->name('backend.assignedemail.index');


});
// Route::get('/dlHit/storedata', $backend_controller_path. '\EmailJsonDataGetController@dlHitAndStoreData');

Route::middleware(['auth', 'check.permission:view_canned_messagers'])->group(function () use ($backend_controller_path) {

Route::get('cannedmessager/index', $backend_controller_path . '\CannedMessagerController@index')->name('backend.cannedmenu.index');
Route::post('canned-message/store', $backend_controller_path . '\CannedMessagerController@saveFormData')->name('backend.cannedmenu.store');
Route::get('canned-message/show', $backend_controller_path . '\CannedMessagerController@indexJson')->name('backend.cannedmenu.indexJson');
Route::post('canned-message/action', $backend_controller_path . '\CannedMessagerController@action')->name('backend.cannedmenu.action');
Route::get('canned-message/edit/{id}', $backend_controller_path . '\CannedMessagerController@edit')->name('backend.cannedmenu.edit');
Route::put('canned-message/update/{id}', $backend_controller_path . '\CannedMessagerController@update')->name('canned-message.update');
Route::delete('canned-message/delete/{id}', $backend_controller_path . '\CannedMessagerController@delete')->name('canned-message.delete');
});

// meta_routesapi
Route::middleware(['auth', 'check.permission:view_meta_routesapi'])->group(function () use ($backend_controller_path) {

$backend_controller_path = 'App\Http\Controllers\Backend';
Route::get('admin/meta_routesapi/index', $backend_controller_path . '\MetahittableController@index')->name('backend.meta_routesapi.index');
Route::get('admin/meta_routesapi/index_data', $backend_controller_path . '\MetahittableController@index_data')->name('backend.meta_routesapi.index_data');
Route::get('admin/meta_routesapi/{id}/show', $backend_controller_path . '\MetahittableController@show')->name('backend.meta_routesapi.show');
Route::post('admin/meta_routesapi/search', $backend_controller_path . '\MetahittableController@search')->name('backend.meta_routesapi.search');
});
// importcsv
Route::middleware(['auth', 'check.permission:view_meta_importcsv'])->group(function () use ($backend_controller_path) {
    $backend_controller_path = 'App\Http\Controllers\Backend';

    Route::get('admin/importcsv/index', $backend_controller_path . '\ImportcsvController@index')->name('backend.importcsvs.index');
    Route::post('admin/importcsv/import', $backend_controller_path . '\ImportcsvController@import')->name('backend.importcsvs.import');
    Route::get('admin/importcsv/export', $backend_controller_path . '\ImportcsvController@export')->name('backend.importcsvs.export');
});

//importRouteBlocking

Route::get('admin/importRouteBlocking/index', $backend_controller_path . '\ImportRouteBlockingController@index')->name('backend.imports.index');
Route::get('admin/importRouteBlocking/logs',$backend_controller_path . '\ImportRouteBlockingController@logIndex')->name('backend.imports.logIndex');
Route::post('admin/importRouteBlocking/import', $backend_controller_path . '\ImportRouteBlockingController@import')->name('backend.imports.import');
Route::get('admin/importRouteBlocking/createimport', $backend_controller_path . '\ImportRouteBlockingController@createimport')->name('backend.imports.createimport');
Route::get('admin/importRouteBlocking/createFormImport', $backend_controller_path . '\ImportRouteBlockingController@createFormImport')->name('backend.imports.createFormImport');
Route::post('admin/importRouteBlocking/storeFormImport', $backend_controller_path . '\ImportRouteBlockingController@storeFormImport')->name('backend.imports.storeFormImport');
Route::get('admin/importRouteBlocking/view/{id}', $backend_controller_path . '\ImportRouteBlockingController@view')->name('backend.imports.view');
Route::delete('admin/importRouteBlocking/destroy/{id}', $backend_controller_path . '\ImportRouteBlockingController@destroy')->name('backend.imports.destroy');
Route::post('admin/importRouteBlocking/deleteRoute', $backend_controller_path . '\ImportRouteBlockingController@deleteRoute')->name('backend.imports.deleteRoute');
Route::get('admin/importRouteBlocking/filterRoute', $backend_controller_path.'\ImportRouteBlockingController@filterRoute')->name('backend.imports.filterRoute');

// metaportal

Route::middleware(['auth', 'check.permission:view_portal_settings'])->group(function () use ($backend_controller_path) {
    Route::get('admin/meta_portal/index', $backend_controller_path . '\MetaPortalController@index')->name('backend.meta_portal.index');
Route::get('admin/meta_portal/create', $backend_controller_path . '\MetaPortalController@create')->name('backend.meta.metaportalcreate');
Route::post('admin/meta_portal/store', $backend_controller_path . '\MetaPortalController@store')->name('backend.meta.metaapi');
Route::get('admin/meta_portal/edit/{id}', $backend_controller_path . '\MetaPortalController@edit')->name('backend.meta_portal.edit');
Route::post('admin/meta_portal/update/{id}', $backend_controller_path . '\MetaPortalController@update')->name('backend.meta_portal.update');
Route::delete('admin/meta_portal/destroy/{id}', $backend_controller_path . '\MetaPortalController@destroy')->name('backend.meta_portal.destroy');
Route::get('admin/meta_portal/timezonelist', $backend_controller_path . '\MetaPortalController@timezonelist')->name('backend.meta_portal.timezonelist');
Route::get('admin/meta_portal/apikey', $backend_controller_path . '\MetaPortalController@apikey')->name('backend.meta_portal.apikey');

// api controller
$backend_controller_path = 'App\Http\Controllers\Backend';
Route::post('admin/api/index', $backend_controller_path . '\GetApiController@search')->name('backend.api.search');
Route::get('admin/api/meta_routes', $backend_controller_path . '\GetApiController@meta_routes')->name('backend.api.meta_routes');
});

// repricing markup
Route::middleware(['auth', 'check.permission:view_airline_reprice_fare'])->group(function () use ($backend_controller_path) {

Route::get('admin/airlinerepricefare/index', $backend_controller_path . '\AirlineRepriceFareController@index')->name('backend.airlinerepricefare.index');
Route::post('admin/airlinerepricefare/update/{id}', $backend_controller_path . '\AirlineRepriceFareController@update')->name('backend.airlinerepricefare.update');
Route::get('admin/airlinerepricefare/create', $backend_controller_path . '\AirlineRepriceFareController@create')->name('backend.airlinerepricefare.create');
Route::get('admin/airlinerepricefare/store', $backend_controller_path . '\AirlineRepriceFareController@store')->name('backend.airlinerepricefare.store');
Route::get('admin/airlinerepricefare/edit/{id}', $backend_controller_path . '\AirlineRepriceFareController@edit')->name('backend.airlinerepricefare.edit');
Route::delete('admin/airlinerepricefare/destroy/{id}', $backend_controller_path . '\AirlineRepriceFareController@destroy')->name('backend.airlinerepricefare.destroy');
Route::post('admin/airlinerepricefare/updateDatabase', $backend_controller_path . '\AirlineRepriceFareController@updateDatabase')->name('backend.airlinerepricefare.updateDatabase');
Route::post('admin/airlinerepricefare/get_airlines', $backend_controller_path . '\AirlineRepriceFareController@get_airlines')->name('backend.airlinerepricefare.get_airlines');
});

// hittable
Route::middleware(['auth', 'check.permission:view_hittable'])->group(function () use ($backend_controller_path) {

$backend_controller_path = 'App\Http\Controllers\Backend';
Route::get('admin/hittable/index', $backend_controller_path . '\HittableController@index')->name('backend.hittable.index');
Route::get('admin/hittable/index_data', $backend_controller_path . '\HittableController@index_data')->name('backend.hittable.index_data');
Route::get('admin/hittable/index_list', $backend_controller_path . '\HittableController@index_list')->name('backend.hittable.index_list');
Route::get('admin/hittable/{id}/edit', $backend_controller_path . '\HittableController@edit')->name('backend.hittable.edit');
Route::get('admin/hittable/{id}/show', $backend_controller_path . '\HittableController@show')->name('backend.hittable.show');
Route::get('admin/hittable/filter', $backend_controller_path . '\HittableController@indexFilter')->name('backend.hittable.filter');
});

//meta sector
Route::middleware(['auth', 'check.permission:view_booking_form_rules'])->group(function () use ($backend_controller_path) {

$backend_controller_path = 'App\Http\Controllers\Backend';
Route::get('admin/meta_sector/index', $backend_controller_path . '\MetasectorController@index')->name('backend.meta_sector.index');
Route::get('admin/meta_sector/create', $backend_controller_path . '\MetasectorController@create')->name('backend.meta_sector.create');
Route::post('admin/meta_sector/metaroutes_store', $backend_controller_path . '\MetasectorController@metaroutes_store')->name('backend.meta_sector.metaroutes_store');
Route::get('admin/meta_sector/edit/{id}', $backend_controller_path . '\MetasectorController@edit')->name('backend.meta_sector.edit');
Route::delete('admin/meta_sector/destroy/{id}', $backend_controller_path . '\MetasectorController@destroy')->name('backend.meta_sector.destroy');
Route::post('admin/meta_sector/update/{id}', $backend_controller_path . '\MetasectorController@update')->name('backend.meta_sector.update');
});
// MetaRouteController

Route::middleware(['auth', 'check.permission:view_portal_settings'])->group(function () use ($backend_controller_path) {

Route::get('admin/meta_routes/getSectorData/{id}', $backend_controller_path . '\MetaRouteController@getSectorData')->name('backend.meta_routes.getSectorData');
Route::get('admin/meta_routes/create', $backend_controller_path . '\MetaRouteController@create')->name('backend.meta_routes.create');
Route::post('admin/meta_routes/metaroutes_store', $backend_controller_path . '\MetaRouteController@metaroutes_store')->name('backend.meta_routes.metaroutes_store');
Route::get('admin/meta_routes/sector_create/{id}', $backend_controller_path . '\MetaRouteController@sector_create')->name('backend.meta_routes.sector_create');
Route::get('admin/meta_routes/city_pair_delete/{id}', $backend_controller_path . '\MetaRouteController@city_pair_delete')->name('backend.meta_routes.city_pair_delete');
Route::get('admin/meta_routes/city_pair_edit/{id}', $backend_controller_path . '\MetaRouteController@city_pair_edit')->name('backend.meta_routes.city_pair_edit');
Route::post('admin/meta_routes/update_citypair/{id}', $backend_controller_path . '\MetaRouteController@update_citypair')->name('backend.meta_routes.update_citypair');
Route::get('admin/meta_routes/edit/{id}', $backend_controller_path . '\MetaRouteController@edit')->name('backend.meta_routes.edit');
Route::get('admin/meta_routes/csrftoken', $backend_controller_path . '\MetaRouteController@csrftoken')->name('backend.meta_routes.csrftoken');
Route::get('admin/meta_routes/get_origin_country/{id}', $backend_controller_path . '\MetaRouteController@get_origin_country')->name('backend.meta_routes.get_origin_country');
Route::post('admin/meta_routes/get_country', $backend_controller_path . '\MetaRouteController@get_country')->name('backend.meta_routes.get_country');
Route::post('admin/meta_routes/update/{id}', $backend_controller_path . '\MetaRouteController@update')->name('backend.meta_routes.update');
Route::delete('admin/meta_routes/destroy/{id}', $backend_controller_path . '\MetaRouteController@destroy')->name('backend.meta_routes.destroy');
Route::post('admin/meta_routes/update/{id}', $backend_controller_path . '\MetaRouteController@destroy')->name('backend.meta_routes.update');

});

	// paymentgatway

	Route::middleware(['auth', 'check.permission:view_payment_gateways'])->group(function () use ($backend_controller_path) {

        Route::get('admin/paymentgatesway/index', $backend_controller_path . '\PaymentGatewaysController@index')->name('backend.paymentgatesway.index');
        Route::post('admin/paymentgatesway/genarateJson', $backend_controller_path . '\PaymentGatewaysController@genarateJson')->name('backend.paymentgatesway.genarateJson');

    });

	Route::middleware(['auth', 'check.permission:view_payment_gateways_failure_rule'])->group(function () use ($backend_controller_path) {

        Route::get('admin/failurepaymentgateway/index', $backend_controller_path . '\PaymentGatewaysFailureController@index')->name('backend.failurepaymentgateway.index');
        Route::post('admin/failurepaymentgateway/genarateJson', $backend_controller_path . '\PaymentGatewaysFailureController@genarateJson')->name('backend.failurepaymentgateway.genarateJson');
        Route::get('admin/failfareupgrade/index', $backend_controller_path . '\FailFareUpgradeController@index')->name('backend.failfareupgrade.index');

        Route::post('admin/failfareupgrade/genarateJson', $backend_controller_path . '\FailFareUpgradeController@genarateJson')->name('backend.failfareupgrade.genarateJson');

    });



//Fare upgrade Baggage



  Route::get('admin/airlinesbaggage/baggages', $backend_controller_path . '\AirlinesBaggage@baggageIndex')->name('backend.airlinesbaggage.main');
  Route::post('admin/airlinesbaggage/update', $backend_controller_path . '\AirlinesBaggage@updateData')->name('backend.airlinesbaggage.update');
  Route::post('admin/airlinesbaggage/update2', $backend_controller_path . '\AirlinesBaggage@updateData2')->name('backend.airlinesbaggage.update2');

// TeamController

  Route::get('/teams', [TeamController::class, 'index'])->name('backend.teams.index');

  // web
  Route::get('/bookingnotification', function (){
    $jsonString='{"SearchClass":"Economy","OriginCity1":"YTO","DestinationCity1":"RIO","OriginCity2":"RIO","DestinationCity2":"YTO","TripType":"RoundTrip","PointOfSaleCountry":"CA","DisplayedPrice":"1101.16","DisplayedPriceCurrency":"CAD","UserLanguage":"en","UserCurrency":"CAD","Adult":"1","Child":"0","InfantLap":"0","Slice1":"1,2","Slice2":"3,4,5","Cabin1":"Economy","Carrier1":"LA","DepartureDate1":"2024-05-18","DepartureTime1":"14:14","ArrivalDate1":"2024-05-18","ArrivalTime1":"16:31","Origin1":"YYZ","Destination1":"ATL","BookingCode1":"V","FlightNumber1":"7018","Cabin2":"Economy","Carrier2":"LA","DepartureDate2":"2024-05-18","DepartureTime2":"20:00","ArrivalDate2":"2024-05-19","ArrivalTime2":"06:35","Origin2":"ATL","Destination2":"GIG","BookingCode2":"V","FlightNumber2":"6347","Cabin3":"Economy","Carrier3":"LA","DepartureDate3":"2024-05-27","DepartureTime3":"07:15","ArrivalDate3":"2024-05-27","ArrivalTime3":"10:45","Origin3":"GIG","Destination3":"LIM","BookingCode3":"X","FlightNumber3":"2405","Cabin4":"Economy","Carrier4":"LA","DepartureDate4":"2024-05-27","DepartureTime4":"12:00","ArrivalDate4":"2024-05-27","ArrivalTime4":"19:00","Origin4":"LIM","Destination4":"MIA","BookingCode4":"X","FlightNumber4":"2694","Cabin5":"Economy","Carrier5":"AC","DepartureDate5":"2024-05-27","DepartureTime5":"21:15","ArrivalDate5":"2024-05-28","ArrivalTime5":"00:30","Origin5":"MIA","Destination5":"YYZ","BookingCode5":"S","FlightNumber5":"1647","mTime":"2024-04-09T08:58:18.954308Z","ugid":"1277b326-17ea-42b4-a218-b795f5edabb9","ReferralId":"btgfs"}';
  $data = json_decode($jsonString, true);

    event(new triggerMessageEvent($data));
    // dd("booking-events");
    return "booking-events";
  });

  Route::get('/mailnotification', function (){
    event(new emailEvents());
    // dd("mail-events");
    return "mail-events";
  });
// fareupgradeController
Route::middleware(['auth', 'check.permission:view_fareupgrade_rules'])->group(function () use ($backend_controller_path) {

    $backend_controller_path = 'App\Http\Controllers\Backend';

    // Route::get('admin/fareupgrade/index', $backend_controller_path . '\fareupgradeController@index')->name('backend.fareupgrade.index');
    Route::get('admin/fareupgrade/index/{source?}/{pos?}', [$backend_controller_path . '\fareupgradeController', 'index'])->name('backend.fareupgrade.index.portal');
    Route::post('admin/fareupgrade/updatedata', $backend_controller_path . '\fareupgradeController@updatedata')->name('backend.fareupgrade.updatedata');
    Route::post('admin/fareupgrade/updateflexibledata', $backend_controller_path . '\fareupgradeController@updateFlexibleTicket')->name('backend.fareupgrade.updateflexibledata');
    Route::post('admin/fareupgrade/updateupgradedata', $backend_controller_path . '\fareupgradeController@updateupgradedata')->name('backend.fareupgrade.updateupgradedata');
    Route::post('admin/fareupgrade/templatejson', $backend_controller_path . '\fareupgradeController@templatejson')->name('backend.fareupgrade.templatejson');
    Route::post('admin/fareupgrade/upgradeterms', $backend_controller_path . '\fareupgradeController@upgradeterms')->name('backend.fareupgrade.upgradeterms');
    Route::post('admin/paymentusercountry/create', $backend_controller_path . '\UserCountryController@create')->name('backend.paymentusercountry.create');
    Route::get('admin/paymentusercountry/index', $backend_controller_path . '\UserCountryController@index')->name('backend.paymentusercountry.index');
    Route::post('admin/fareupgrade/update-source', $backend_controller_path . '\fareupgradeController@updateSource')->name('update.source');
    Route::post('admin/fareupgrade/create-process', $backend_controller_path . '\fareupgradeController@createProcessData')->name('create.process');
    Route::get('admin/fareupgrade/get-flexible-ticket-data', $backend_controller_path . '\fareupgradeController@getFlexibleTicketData')->name('create.getFlexibleTicketData');

    });


        Route::get('xml/index','App\Http\Controllers\Backend\XmlChangeAndCancelRules@index')->name('backend.xml.index');

// signature
Route::middleware(['auth','check.permission:view_Signature'])->group(function() use($backend_controller_path){
    $backend_controller_path = 'App\Http\Controllers\Backend';
    Route::get('admin/signature/index' ,$backend_controller_path . '\SignatureController@index')->name('backend.signature.index');
    Route::post('admin/signature/saveFormData' ,$backend_controller_path . '\SignatureController@saveFormData')->name('backend.signature.saveFormData');
    Route::get('admin/signature/indexJson' ,$backend_controller_path . '\SignatureController@indexJson')->name('backend.signature.indexJson');
    Route::post('admin/signature/action', $backend_controller_path . '\SignatureController@action')->name('backend.signature.action');
    Route::get('admin/signature/edit/{id}', $backend_controller_path . '\SignatureController@edit')->name('backend.signature.edit');
    Route::put('admin/signature/update/{id}', $backend_controller_path . '\SignatureController@update')->name('backend.signature.update');
    Route::delete('admin/signature/delete/{id}', $backend_controller_path . '\SignatureController@delete')->name('backend.signature.delete');
});
Route::middleware(['auth'])->group(function() use($backend_controller_path){
    $backend_controller_path = 'App\Http\Controllers\Backend';
    // Route::post('admin/chat/response/store' ,$backend_controller_path . '\ChatWebhookController@store')->name('backend.chat.response.store');

});
Route::middleware(['auth'])->group(function() use($backend_controller_path){
    $backend_controller_path = 'App\Http\Controllers\Backend';
    Route::post('admin/chat/get/bookingid' ,$backend_controller_path . '\PaxChatDetailsController@show')->name('backend.chat.get.bookingid');

});
Route::middleware(['auth'])->group(function() use($backend_controller_path){
Route::get('admin/chatconversation/index', $backend_controller_path . '\ChatConversationController@index')->name('backend.chatconverstion.index');
Route::get('admin/chatconversation/index_data', $backend_controller_path . '\ChatConversationController@index_data')->name('backend.chatconverstion.index_data');
Route::post('admin/chatconversation/chat_conversation', $backend_controller_path . '\ChatConversationController@viewChat')->name('backend.chatconverstion.chat_conversation');

Route::get('admin/tickets/index', $backend_controller_path . '\ChatConversationController@ticketIndex')->name('backend.chatconverstion.ticketIndex');
Route::get('admin/tickets/index_ticket', $backend_controller_path . '\ChatConversationController@index_ticket')->name('backend.chatconverstion.index_ticket');
Route::post('admin/tickets/ticketChatDetailsget', $backend_controller_path . '\ChatConversationController@ticketChatDetailsget')->name('backend.chatconverstion.ticketChatDetailsget');
});
Route::middleware(['auth','check.permission:view_failure_bookings'])->group(function() use($backend_controller_path){
    $backend_controller_path = 'App\Http\Controllers\Backend';
    Route::get('admin/failure/booking/list' ,$backend_controller_path . '\PaxFailureBookingRetrieveController@index')->name('backend.failure.booking.list');
    Route::get('admin/failure/booking/index-list' ,$backend_controller_path . '\PaxFailureBookingRetrieveController@index_data');

    });

// Agentgroup

Route::middleware(['auth','check.permission:view_agent_group'])->group(function() use($backend_controller_path){
    $backend_controller_path = 'App\Http\Controllers\Backend';
    Route::get('admin/agentgroup/index' ,$backend_controller_path . '\B2bAgentGroupController@index')->name('backend.agentgroup.index');
    Route::post('admin/agentgroup/store' ,$backend_controller_path . '\B2bAgentGroupController@store')->name('backend.agentgroup.store');
    Route::post('admin/agentgroup/update/{id}' ,$backend_controller_path . '\B2bAgentGroupController@update')->name('backend.agentgroup.update');
    Route::delete('admin/agentgroup/destroy/{id}' ,$backend_controller_path . '\B2bAgentGroupController@destroy')->name('backend.agentgroup.destroy');
    Route::get('admin/agentgroup/trashed' ,$backend_controller_path . '\B2bAgentGroupController@trashed')->name('backend.agentgroup.trashed');
    Route::patch('admin/agentgroup/restore/{id}', $backend_controller_path . '\B2bAgentGroupController@restore')->name('backend.agentgroup.restore');
    Route::get('admin/agentgroup/viewagent/{id}', $backend_controller_path . '\B2bAgentGroupController@RelationData')->name('backend.agentgroup.RelationData');
});
// Agent
Route::middleware(['auth.or.agent','check.permission:view_agent'])->group(function() use($backend_controller_path){
    $backend_controller_path = 'App\Http\Controllers\Backend';
    Route::get('admin/agent/index' ,$backend_controller_path . '\B2bAgentController@index')->name('backend.agent.index');
    Route::post('admin/agent/store' ,$backend_controller_path . '\B2bAgentController@store')->name('backend.agent.store');
    Route::post('admin/agent/update/{id}' ,$backend_controller_path . '\B2bAgentController@update')->name('backend.agent.update');
    Route::delete('admin/agent/destroy/{id}' ,$backend_controller_path . '\B2bAgentController@destroy')->name('backend.agent.destroy');
    Route::post('admin/agent/Namevalidate' ,$backend_controller_path . '\B2bAgentController@Namevalidate')->name('backend.agent.Namevalidate');
    Route::post('admin/agent/LastNamevalidate' ,$backend_controller_path . '\B2bAgentController@LastNamevalidate')->name('backend.agent.LastNamevalidate');
    Route::post('admin/agent/LastNamevalidate' ,$backend_controller_path . '\B2bAgentController@LastNamevalidate')->name('backend.agent.LastNamevalidate');
    Route::post('admin/agent/Emailvalidate' ,$backend_controller_path . '\B2bAgentController@Emailvalidate')->name('backend.agent.Emailvalidate');
    Route::post('admin/agent/AgencyNamevalidate' ,$backend_controller_path . '\B2bAgentController@AgencyNamevalidate')->name('backend.agent.AgencyNamevalidate');
    Route::get('admin/agent/agentview/{id}' ,$backend_controller_path . '\B2bAgentController@AgentViewDetails')->name('backend.agent.AgentViewDetails');
    Route::get('admin/agent/trashed' ,$backend_controller_path . '\B2bAgentController@trashed')->name('backend.agent.trashed');
    Route::get('admin/agent/agentfilter' ,$backend_controller_path . '\B2bAgentController@AgentFilter')->name('backend.agent.AgentFilter');
    Route::patch('admin/agent/restore/{id}', $backend_controller_path . '\B2bAgentController@restore')->name('backend.agent.restore');
});

//b2cusers crud
$backend_controller_path = 'App\Http\Controllers\Backend';
    Route::get('admin/b2cusers/index', $backend_controller_path . '\B2cUserController@index')->name('backend.b2cusers.index');
    Route::post('admin/b2cusers/update/{id}', $backend_controller_path . '\B2cUserController@update')->name('backend.b2cusers.update');
    Route::delete('admin/b2cusers/destroy/{id}', $backend_controller_path . '\B2cUserController@destroy')->name('backend.b2cusers.destroy');
    Route::get('admin/b2cusers/b2cUserFilter', $backend_controller_path . '\B2cUserController@b2cUserFilter')->name('backend.b2cusers.b2cUserFilter');
    Route::get('admin/b2cusers/trashed' ,$backend_controller_path . '\B2cUserController@trashed')->name('backend.b2cusers.trashed');
    Route::patch('admin/b2cusers/restore/{id}', $backend_controller_path . '\B2cUserController@restore')->name('backend.b2cusers.restore');
    Route::post('admin/b2cusers/store', $backend_controller_path . '\B2cUserController@store')->name('backend.b2cusers.store');

//PNR retrieve
Route::middleware(['auth.or.agent','check.permission:view_pnr_retrieve'])->group(function() use($backend_controller_path){
Route::get('admin/pnr/retrieve' ,$backend_controller_path . '\PnrRetrieveController@index')->name('backend.pnr.retrieve');
Route::post('admin/pnr/retrievalpnritinerary' ,$backend_controller_path . '\PnrRetrieveController@retrievalPnrAndItinerary')->name('backend.pnr.retrieve.pnr.Itinerary');
Route::post('admin/pnr/retrievalsinglepnranditinerary' ,$backend_controller_path . '\PnrRetrieveController@retrievalSinglePnrAndItinerary')->name('backend.single.pnr.retrieve.pnr.Itinerary');
Route::post('admin/pnr/data/insertion' ,$backend_controller_path . '\PnrRetrieveController@DataInsertion')->name('backend.pnr.data.insertion');
Route::post('admin/pnr/multi/data/insertion' ,$backend_controller_path . '\PnrRetrieveController@MultiDataInsertion')->name('backend.pnr.multi.data.insertion');
Route::post('admin/pnr/retrieve/history' ,$backend_controller_path . '\PnrRetrieveController@pnrHistory')->name('backend.pnrretrieve.history');
Route::post('admin/cancelBooking', $backend_controller_path . '\PnrRetrieveController@bookingCancel')->name('backend.pnrretrieve.bookingcancel');
});

//accounting
Route::middleware(['auth.or.agent'])->group(function () use($backend_controller_path){
    Route::get('admin/accounting/commission',$backend_controller_path . '\AccountingController@index')->name('backend.accounting.commission');
    Route::get('admin/accounting/invoice',$backend_controller_path . '\AccountingController@invoiceView')->name('backend.accounting.invoice');
    Route::post('admin/accounting/get/invoice',$backend_controller_path . '\AccountingController@getInvoiceView')->name('backend.accounting.get.invoice');
    Route::post('admin/process-data', $backend_controller_path . '\AccountingController@processData')->name('process.data');
    Route::get('admin/agency/accounting/index',$backend_controller_path . '\AgencyAccountingController@index')->name('backend.agency.accounting.index');

    //filter route (newly added)
    // Route::post('admin/agency/accounting/index',$backend_controller_path . '\AgencyAccountingController@index')->name('backend.agency.accounting.indexfilter');

    Route::get('admin/airline/accounting/index',$backend_controller_path . '\AirlineAccountingController@index')->name('backend.airline.accounting.index')->middleware('can:view_airline_accounting');
    Route::post('admin/accounting/invoice/save',$backend_controller_path . '\AccountingController@invoicePdfSave')->name('backend.accounting.invoice.save');
});
//agent
Route::middleware(['auth.or.agent','check.permission:view_registrations_agent'])->group(function () use($backend_controller_path){
    Route::get('admin/agents/index',$backend_controller_path . '\AgentController@index')->name('backend.newregistration.agent');
    Route::get('admin/agency/getagentData',$backend_controller_path . '\AgentController@getAgentData')->name('backend.newregistration.getagentData');
    Route::get('admin/agents/agentview',$backend_controller_path . '\AgentController@Agentview')->name('backend.newregistration.agentview');
    Route::get('admin/agents/restore/{id}',$backend_controller_path . '\AgentController@restore')->name('backend.newregistration.restore');
    Route::delete('admin/agency/delete/{id}',$backend_controller_path . '\AgentController@delete')->name('backend.newregistration.delete');
});
//agency
Route::middleware(['auth.or.agent','check.permission:view_agency'])->group(function () use($backend_controller_path){
    Route::get('admin/agency/index',$backend_controller_path . '\AgencyController@index')->name('backend.newregistration.agency');
    Route::post('admin/agency/register/store' ,$backend_controller_path . '\AgencyController@store')->name('backend.agency.register.store');
    Route::get('admin/agency/register/store/list' ,$backend_controller_path . '\AgencyController@agencyAcceptAndDecline')->name('backend.agency.register.store.list');
});

//agency Accounting
// Route::middleware(['auth.or.agent','check.permission:view_agency_accounting'])->group(function () use($backend_controller_path){
    //     Route::get('admin/agency/accounting/index',$backend_controller_path . '\AgencyAccountingController@index')->name('backend.agency/accounting.index');
    // });

    //airline Accounting
    // Route::middleware(['auth.or.agent','check.permission:view_airline_accounting'])->group(function () use($backend_controller_path){
        //     Route::get('admin/airline/accounting/index',$backend_controller_path . '\AirlineAccountingController@index')->name('backend.airline/accounting.index');
        // });

        Route::post('admin/agency/registration',$backend_controller_path . '\AgencyRegistrationController@storeAgencyRegister')->name('agency.registration');
        Route::post('admin/agent/registration',$backend_controller_path . '\AgentRegistrationController@storeAgentRegister')->name('agent.registration');



        //cache management
        Route::get('/admin/cache' ,$backend_controller_path . '\CacheController@index')->name('backend.cache');
        Route::post('/admin/cache/clear' ,$backend_controller_path . '\CacheController@clearCache')->name('system.cache.clear');

        Route::get('admin/email/signature',$backend_controller_path . '\EmailSignatureTemplate@index')->name('backend.email.signature');
        Route::post('admin/email/signature/store',$backend_controller_path . '\EmailSignatureTemplate@store')->name('backend.email.signature.store');
        Route::post('admin/email/signaturestore',$backend_controller_path . '\EmailSignatureTemplate@emailSignatureStore')->name('backend.email.signaturestore');
        Route::post('admin/email/signature/find',$backend_controller_path . '\EmailSignatureTemplate@emailSignatureFind')->name('backend.email.signature.find');

        Route::get('admin/net2phone/index',$backend_controller_path . '\net2phoneWebhookController@index')->name('backend.net2phone.index');
        Route::post('admin/net2phone/answer/call',$backend_controller_path . '\net2phoneWebhookController@AnswerCall')->name('backend.net2phone.answer.call');
        Route::get('admin/net2phone/list/active/calls',$backend_controller_path . '\net2phoneWebhookController@listActiveCalls')->name('backend.net2phone.listactive.call');

Route::get('admin/invoice/signature',$backend_controller_path . '\InvoiceSignatureTemplate@index')->name('backend.invoice.signature');
Route::post('admin/invoice/signature/store',$backend_controller_path . '\InvoiceSignatureTemplate@store')->name('backend.invoice.signature.store');
Route::post('admin/invoice/signaturestore',$backend_controller_path . '\InvoiceSignatureTemplate@invoiceSignatureStore')->name('backend.invoice.signaturestore');
Route::post('admin/invoice/signaturefind',$backend_controller_path . '\InvoiceSignatureTemplate@invoiceSignatureFind')->name('backend.invoice.signature.find');

Route::post('admin/airline/accounting/status',$backend_controller_path . '\AirlineAccountingController@status')->name('backend.accounting.commission.status');
Route::post('admin/agency/accounting/processData',$backend_controller_path . '\AgencyAccountingController@processData')->name('backend.accounting.commission.processData');
// agentnotes
Route::post('admin/agentnotes',$backend_controller_path . '\AgentNotesController@AgentNoteStore')->name('backend.agentnotes');

// card details
Route::post('admin/cardDetails' ,$backend_controller_path . '\CardDetailsController@index')->name('backend.cardDetails');
Route::post('admin/cardDetails/edit' ,$backend_controller_path . '\CardDetailsController@edit')->name('backend.cardDetails.edit');
Route::post('admin/cardDetails/update' ,$backend_controller_path . '\CardDetailsController@update')->name('backend.cardDetails.update');
Route::post('admin/cardDetails/update/amount' ,$backend_controller_path . '\CardDetailsController@updateAmount')->name('backend.cardDetails.update.amount');
Route::post('admin/cardDetails/deactivate' ,$backend_controller_path . '\CardDetailsController@deactivate')->name('backend.card.deactivated');

// Mail Configuration
Route::post('admin/mailConfiguration/form' ,$backend_controller_path . '\MailConfigurationController@index')->name('backend.mailConfigurationForm');
Route::post('admin/mailConfiguration/button' ,$backend_controller_path . '\MailConfigurationController@buttonEdit')->name('backend.mailConfigurationBtn');
Route::post('admin/mailConfiguration/test/form' ,$backend_controller_path . '\MailConfigurationController@testForm')->name('backend.mailConfigurationTest');
Route::post('admin/mailConfiguration/defaultForm' ,$backend_controller_path . '\MailConfigurationController@defaultForm')->name('backend.defaultForm');

Route::post('admin/invoice/headerstore',$backend_controller_path . '\InvoiceHeaderTemplateController@invoiceHeaderStore')->name('backend.invoice.header.store');
Route::post('admin/invoice/headerfind',$backend_controller_path . '\InvoiceHeaderTemplateController@invoiceHeaderFind')->name('backend.invoice.header.find');


//CommissionPlan
Route::middleware(['auth.or.agent'])->group(function () use($backend_controller_path){
    Route::get('admin/commissionplan/index',$backend_controller_path . '\CommissionPlanController@index')->name('backend.commissionplan.index');
    Route::post('admin/commissionplan/store',$backend_controller_path . '\CommissionPlanController@store')->name('backend.commissionplan.store');
    Route::post('admin/commissionplan/update/{id}',$backend_controller_path . '\CommissionPlanController@update')->name('backend.commissionplan.update');

});
// B2c Agentgroup
Route::middleware(['auth'])->group(function() use($backend_controller_path){
    $backend_controller_path = 'App\Http\Controllers\Backend';
    Route::get('admin/portal/index' ,$backend_controller_path . '\B2cPortalController@index')->name('backend.b2c_portal.index');
    Route::post('admin/portal/store' ,$backend_controller_path . '\B2cPortalController@store')->name('backend.b2c_portal.store');
    Route::post('admin/portal/update/{id}' ,$backend_controller_path . '\B2cPortalController@update')->name('backend.b2c_portal.update');
    Route::delete('admin/portal/destroy/{id}' ,$backend_controller_path . '\B2cPortalController@destroy')->name('backend.b2c_portal.destroy');
    Route::get('admin/portal/trashed' ,$backend_controller_path . '\B2cPortalController@trashed')->name('backend.b2c_portal.trashed');
    Route::patch('admin/portal/restore/{id}', $backend_controller_path . '\B2cPortalController@restore')->name('backend.b2c_portal.restore');
});
Route::post('/check-b2c_group_id', [B2cPortalController::class, 'checkAgentGroupId'])->name('check.b2c_group_id');
Route::middleware(['auth'])->group(function() use($backend_controller_path){
    $backend_controller_path = 'App\Http\Controllers\Backend';
    Route::get('admin/support/index' ,$backend_controller_path . '\SupportController@index')->name('backend.support.index');
    Route::post('admin/support/store' ,$backend_controller_path . '\SupportController@store')->name('backend.support.store');
    Route::get('admin/support/show/{id}' ,$backend_controller_path . '\SupportController@show')->name('backend.support.show');
    Route::get('support/download/{filename}' ,$backend_controller_path . '\SupportController@download')->name('backend.support.download');


});
Route::middleware(['auth','check.permission:view_agent_group'])->group(function() use($backend_controller_path){
    $backend_controller_path = 'App\Http\Controllers\Backend';
    Route::get('admin/agencygroup/index' ,$backend_controller_path . '\B2bAgencyGroupController@index')->name('backend.agencygroup.index');
    Route::post('admin/agencygroup/store' ,$backend_controller_path . '\B2bAgencyGroupController@store')->name('backend.agencygroup.store');
    Route::post('admin/agencygroup/update/{id}' ,$backend_controller_path . '\B2bAgencyGroupController@update')->name('backend.agencygroup.update');
    Route::get('admin/agencygroup/trashed' ,$backend_controller_path . '\B2bAgencyGroupController@trashed')->name('backend.agencygroup.trashed');
});
Route::middleware(['auth','check.permission:view_agent'])->group(function() use($backend_controller_path){
    $backend_controller_path = 'App\Http\Controllers\Backend';
    Route::get('admin/agency/agent/index' ,$backend_controller_path . '\B2bAgencyAgentController@index')->name('backend.agencyagent.index');
    Route::get('admin/agency/agent/trashed' ,$backend_controller_path . '\B2bAgencyAgentController@trashed')->name('backend.agencyagent.trashed');
    Route::patch('admin/agency/agent/restore/{id}', $backend_controller_path . '\B2bAgencyAgentController@restore')->name('backend.agencyagent.restore');
    Route::get('admin/agency/agent/agentfilter' ,$backend_controller_path . '\B2bAgencyAgentController@AgentFilter')->name('backend.agencyagent.AgentFilter');
});
Route::post('admin/bookings/status/retrieve', $backend_controller_path . '\PaxdetailsController@BookingStatusRetrieve')->name('bookings.status.retrieve');
Route::post('bookings/sendInvoiceTemplateMail', $backend_controller_path . '\AccountingController@sendInvoiceTemplateMail')->name('booking.sendInvoiceTemplateMail');

Route::post('admin/accounting/invoice/generate/save',$backend_controller_path . '\AccountingController@invoiceGeneratePdfSave')->name('backend.accounting.invoice.generate.save');


//content Editor
Route::get('admin/editor/index' ,$backend_controller_path . '\ContentEditorController@index')->name('backend.editor.index');
Route::post('admin/editor/post' ,$backend_controller_path . '\ContentEditorController@dataPost')->name('backend.editor.post');

// Meta data
Route::get('admin/meta/data/{type?}' ,$backend_controller_path . '\MetaDataController@form')->name('backend.metaData');
Route::post('admin/meta/data' ,$backend_controller_path . '\MetaDataController@store')->name('backend.meta.store');
Route::post('admin/meta/data/json-read' ,$backend_controller_path . '\MetaDataController@readJson')->name('backend.metaDataRead');

// seat business
Route::get('admin/seat/feerule' , $backend_controller_path . '\SeatBusinessController@index')->name('seat.feerule');
Route::post('admin/seat/feerule/create' , $backend_controller_path . '\SeatBusinessController@seatfeerulecreate')->name('backend.seatfeerule.create');
Route::delete('admin/seat/feerule/destroy/{id}' , $backend_controller_path . '\SeatBusinessController@seatfeeruledelete')->name('backend.seatFeerule.destroy');
Route::post('admin/seat/feerule/update/{id}' , $backend_controller_path . '\SeatBusinessController@seatfeeruleupdate')->name('backend.seatfeerule.update');
Route::get('admin/seat/feerule/filter' , $backend_controller_path . '\SeatBusinessController@filterindex')->name('backend.seatfeerule.filterindex');

// seat default business rules
Route::get('admin/seat/default/feerule' , $backend_controller_path . '\SeatDefaultBusinessController@index')->name('seat.default.feerule');
Route::post('admin/seat/default/feerule/create' , $backend_controller_path . '\SeatDefaultBusinessController@seatfeerulecreate')->name('backend.defaultseatfeerule.create');
Route::delete('admin/seat/default/feerule/destroy/{id}' , $backend_controller_path . '\SeatDefaultBusinessController@seatfeeruledelete')->name('backend.defaultseatFeerule.destroy');
Route::post('admin/seat/default/feerule/update/{id}' , $backend_controller_path . '\SeatDefaultBusinessController@seatfeeruleupdate')->name('backend.defaultseatfeerule.update');
Route::get('admin/seat/default/feerule/filter' , $backend_controller_path . '\SeatDefaultBusinessController@filterindex')->name('backend.defaultseatfeerule.filterindex');


// Search and booking list
Route::get('admin/booking/log-data' ,$backend_controller_path . '\TestController@jsonDaa');


// Agency Account Details
Route::post('/backend/agencygroup/deposit' ,$backend_controller_path . '\AgencyAccountDetailsController@store')->name('backend.agencygroup.deposit');
Route::post('/backend/agencygroup/formdata/store' ,$backend_controller_path . '\AgencyAccountDetailsController@storeFormData')->name('backend.agencygroup.formdata.store');
Route::post('/backend/deposit/calculation/{id}' ,$backend_controller_path . '\AgencyAccountDetailsController@depositCalculation')->name('backend.agencygroup.deposit.calculation');
Route::get('/backend/all/transaction/{id}' ,$backend_controller_path . '\AgencyAccountDetailsController@allTransaction');
Route::get('/backend/agentAccount/details' ,$backend_controller_path . '\AgencyAccountDetailsController@agentAccountDetails');


Route::get('/insertFakeData' ,$backend_controller_path . '\TestController@insertFakeData');

Route::get('/generate/email' ,$backend_controller_path . '\EmailGeneratorController@index');

//currency based blocking
Route::get('/admin/currency-based/blocking' ,$backend_controller_path . '\CurrencyBasedBlockingController@index');
Route::post('/admin/currency-based/blocking/store' ,$backend_controller_path . '\CurrencyBasedBlockingController@store')->name('backend.currencybasedblocking.store');
Route::post('/admin/commissionplan/update/{id}' ,$backend_controller_path . '\CurrencyBasedBlockingController@update')->name('backend.currencybasedblocking.update');
Route::delete('/admin/currency-based/blocking/{id}' ,$backend_controller_path . '\CurrencyBasedBlockingController@destroy')->name('backend.currencybasedblocking.destroy');


Route::delete('/admin/feerulegroup/feerulegroupdelete/{id}' ,$backend_controller_path . '\FeeruleGroupController@feerulegroupdelete');

Route::post('airlineblock/status/update', $backend_controller_path . '\AirlineBlockController@airlineBlockStatusUpdate')->name('airlineblock.status.update');

//fare types
Route::get('/admin/fare-types', $backend_controller_path . '\FareTypesController@index');
Route::post('/admin/fare-types/store', $backend_controller_path . '\FareTypesController@store')->name('backend.fare-types.store');
Route::post('/admin/fare-type/update/{id}', $backend_controller_path . '\FareTypesController@update');
Route::delete('/admin/fare-type/{id}', $backend_controller_path . '\FareTypesController@destroy')->name('backend.fare-type.destroy');
Route::POST('/admin/edit/faretype/{id}', $backend_controller_path . '\FareTypesController@edit')->name('backend.fare-type.edit');


// booking capture

Route::get('/admin/booking-capture', $backend_controller_path . '\BookingCaptureController@index')->name('backend.booking-capture.index');
Route::post('/admin/booking-capture/store', $backend_controller_path . '\BookingCaptureController@store')->name('backend.booking-capture.store');
Route::delete('/admin/booking-capture/{id}', $backend_controller_path . '\BookingCaptureController@destroy')->name('backend.booking-capture.destroy');
Route::get('/admin/get-agency-details', $backend_controller_path . '\PortalConfigController@getAgencyDetails');

//Portal Api Credential

Route::get('admin/portal-api-credential/index', $backend_controller_path . '\PortalApiCredentialController@index')->name('backend.portalapicredential.index');
Route::post('admin/portal-api-credential/store', $backend_controller_path . '\PortalApiCredentialController@store')->name('backend.portalapicredential.store');
Route::get('admin/portal-api-credential/edit/{id}', $backend_controller_path . '\PortalApiCredentialController@edit')->name('backend.portalapicredential.edit');
Route::post('admin/portal-api-credential/update/{id}', $backend_controller_path . '\PortalApiCredentialController@update')->name('backend.portalapicredential.update');
Route::get('admin/portal-api-credential/trashed', $backend_controller_path . '\PortalApiCredentialController@trashed')->name('backend.portalapicredential.trashed');
Route::post('/admin/bookingCapture/edit/{id}', $backend_controller_path . '\BookingCaptureController@edit')->name('backend.booking-capture.edit');
Route::post('/admin/bookingCapture/update/{id}', $backend_controller_path . '\BookingCaptureController@update')->name('backend.booking-capture.update');


Route::get('/admin/notify-price-drops', $backend_controller_path . '\NotifyPriceDropsController@index')->name('backend.notify-price-drops.index');
Route::post('/admin/notes/update', $backend_controller_path . '\NotifyPriceDropsController@notesUpdate')->name('backend.notify-price-drops.notesUpdate');
Route::get('/admin/notes/view/{id}', $backend_controller_path . '\NotifyPriceDropsController@view')->name('backend.notify.view');


Route::middleware(['auth.or.agent', 'check.permission:view_blog'])->group(function () use ($backend_controller_path) {
  $backend_controller_path = 'App\Http\Controllers\Backend';
  Route::get('admin/blog/index', $backend_controller_path . '\BlogController@index')->name('backend.blog.form');
  Route::post('admin/blog/store', $backend_controller_path . '\BlogController@store')->name('backend.blog.store');
  Route::get('admin/blog/report', $backend_controller_path . '\BlogController@report')->name('backend.blog.report');
  // Route::get('admin/news_letter/notification', $backend_controller_path . '\BlogController@newsletterMailLogs')->name('backend.news_letter.newsletterMailLogs');
  // Route::get('admin/news_letter/newsletterLogIndex', $backend_controller_path . '\BlogController@newsletterLogIndex')->name('backend.news_letter.newsletterLogIndex');
  // Route::get('admin/news_letter/getAllNotifications', $backend_controller_path . '\BlogController@getAllNotifications')->name('backend.news_letter.getAllNotifications');
  Route::get('admin/blog/edit/{id}', $backend_controller_path . '\BlogController@edit')->name('backend.blog.edit');
  Route::post('admin/blog/update', $backend_controller_path . '\BlogController@update')->name('backend.blog.update');
  Route::get('admin/blog/destroy/{id}', $backend_controller_path . '\BlogController@destroy')->name('backend.blog.destroy');
  Route::post('admin/blog/preview', $backend_controller_path . '\BlogController@newsLetterMailPreview')->name('backend.blog.LetterMailPreview');
  Route::post('admin/check_slug', $backend_controller_path . '\BlogController@checkSlug')->name('backend.blog.checkslug');
  Route::get('/admin/tag/report', $backend_controller_path . '\TagController@index');
  Route::post('/admin/tags/store', $backend_controller_path . '\TagController@store')->name('backend.tag.store');
  Route::get('admin/tag/report/edit/{id}', $backend_controller_path . '\TagController@edit')->name('backend.tag.edit');
  Route::post('admin/tag/report/update', $backend_controller_path . '\TagController@update')->name('backend.tag.update');
  Route::delete('admin/tag/report/delete/{id}', $backend_controller_path . '\TagController@destroy')->name('backend.tag.destroy');
});

Route::get('admin/b2c/agencyconfig/{id}', $backend_controller_path . '\B2CAgencyConfigController@index')->name('backend.agencyconfig');
Route::post('admin/b2c/agencyconfig/store', $backend_controller_path . '\B2CAgencyConfigController@store')->name('backend.b2c-agency-config.store');
Route::post('admin/b2c/update', $backend_controller_path . '\B2CAgencyConfigController@update')->name('b2c.update');
Route::get('admin/get-api-keys/{portalId}', $backend_controller_path . '\B2CAgencyConfigController@getApiKeys')->name('backend.get-api-keys');


Route::get('admin/airline_masking/index', $backend_controller_path . '\AirlineMaskingController@index')->name('backend.airline_masking.index');
Route::get('admin/airline_masking/create', $backend_controller_path . '\AirlineMaskingController@create')->name('backend.airline_masking.create');
Route::post('admin/airline_masking/store', $backend_controller_path . '\AirlineMaskingController@store')->name('backend.airline_masking.store');
Route::get('/airline_masking/{id}/edit', $backend_controller_path . '\AirlineMaskingController@edit')->name('backend.airline_masking.edit');
Route::put('/airline_masking/{id}', $backend_controller_path . '\AirlineMaskingController@update')->name('backend.airline_masking.update');
Route::delete('admin/airline_masking/delete/{id}', $backend_controller_path . '\AirlineMaskingController@delete')->name('backend.airline_masking.delete');
Route::post('airline_masking/status/update', $backend_controller_path . '\AirlineMaskingController@updateStatus')->name('backend.airline_masking.status.update');


Route::get('admin/masking_master/create', $backend_controller_path . '\AirlineMaskingController@createMaster')->name('backend.masking_master.create');
Route::post('admin/masking_master/store', $backend_controller_path . '\AirlineMaskingController@storeMaster')->name('backend.masking_master.store');
Route::get('admin/masking_master/index', $backend_controller_path . '\AirlineMaskingController@indexMaster')->name('backend.masking_master.index');
Route::post('admin/masking_master/status/{id}', $backend_controller_path . '\AirlineMaskingController@updateStatusMaster')->name('backend.masking_master.status');
Route::delete('admin/masking_master/delete/{id}', $backend_controller_path . '\AirlineMaskingController@destroyMaster')->name('backend.masking_master.delete');
Route::get('admin/masking_master/edit/{id}', $backend_controller_path . '\AirlineMaskingController@editMaster')->name('backend.masking_master.edit');
Route::put('admin/masking_master/update/{id}', $backend_controller_path . '\AirlineMaskingController@updateMaster')->name('backend.masking_master.update');


Route::get('generateMarkupFeeruleUrl/{id}', $backend_controller_path . '\FeeruleGroupController@generateMarkupFeerule')->name('backend.generateMarkupFeerule');


Route::get('admin/content_sources/create', $backend_controller_path . '\ContentSourcesController@create')->name('backend.content_sources.create');
Route::post('admin/content_sources/store', $backend_controller_path . '\ContentSourcesController@store')->name('backend.content_sources.store');
Route::get('admin/content_sources/index', $backend_controller_path . '\ContentSourcesController@index')->name('backend.content_sources.index');
Route::post('admin/content_sources/status/{id}', $backend_controller_path . '\ContentSourcesController@updateStatus')->name('backend.content_sources.status');
Route::delete('admin/content_sources/delete/{id}', $backend_controller_path . '\ContentSourcesController@destroy')->name('backend.content_sources.delete');
Route::get('admin/content_sources/edit/{id}', $backend_controller_path . '\ContentSourcesController@edit')->name('backend.content_sources.edit');
Route::put('admin/content_sources/update/{id}', $backend_controller_path . '\ContentSourcesController@update')->name('backend.content_sources.update');
