<!-- Modal -->
<div class="modal fade" id="backDropModalGetInvoicesHeader" data-bs-backdrop="static" tabindex="-1">
    <div class="modal-dialog">
        <form class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="backDropModalTitle">Invoice Header</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="message-invoice-header"></div>
                <div class="row fw-bold">
                    <input type="hidden" id="group-id-invoice-header">
                    <div class="col-md-4">
                        <p class="mb-2">Group Name:</p>
                    </div>
                    <div class="col-md-8"><span id="group-name-invoice-header"></span></div>
                    
                </div>
                <div class="row g-2 mt-3">
                    <div id="agent-note">
                        <div class="snow-editor-invoice-header"></div>
                    </div>
                    <div class="d-none">
                        <input type="checkbox" class="form-check-input" name="signatureCheckboxInvoiceHeader" id="signatureCheckboxInvoiceHeader"><label for="signatureCheckboxInvoiceHeader"><span style="margin-left: 3px">Active</span></label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" id="save-button-invoice-header" class="btn btn-primary">Save</button>
            </div>
        </form>
    </div>
</div>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        // Initialize Quill editor
        window.quillInvoiceHeader = new Quill('.snow-editor-invoice-header', {
            bounds: '.snow-editor-invoice-header',
            modules: {
                formula: true,
                toolbar: fullToolbar
            },
            theme: 'snow'
        });

        // Set image dimensions when text changes
        window.quillInvoiceHeader.on('text-change', function(delta, oldDelta, source) {
            if (source === 'user') {
                const images = document.querySelectorAll('.snow-editor-invoice-header img');
                images.forEach(img => {
                    img.style.width = '178px';
                });
            }
        });

        // Handle save button click
        $('#save-button-invoice-header').click(function() {
            if (window.quillInvoiceHeader) {
                let message = document.getElementById('message-invoice-header');
                let groupId = document.getElementById('group-id-invoice-header').value;
                let sig = document.getElementById('signatureCheckboxInvoiceHeader').checked;
                let editorContent = window.quillInvoiceHeader.root.innerHTML;

                console.log('Editor Content:', editorContent);

                // Adjust image sizes in editor content
                editorContent = editorContent.replace(/<img\s+([^>]*?)>/g, function(match, p1) {
                    return `<img ${p1} width="178">`;
                });

                // Make AJAX request
                $.ajax({
                    url: '<?php echo e(route("backend.invoice.header.store")); ?>',
                    type: 'POST',
                    data: {
                        content: editorContent,
                        groupId: groupId,
                        status: sig,
                        _token: '<?php echo e(csrf_token()); ?>'
                    },
                    success: function(response) {
                        let alertDiv = `<div class="alert alert-success mt-2" role="alert">${response.message}</div>`;
                        if (response.status === 'success') {
                            message.innerHTML = alertDiv;
                        } else {
                            message.innerHTML = `<div class="alert alert-danger mt-2" role="alert">${response.message}</div>`;
                        }
                        setTimeout(function() {
                            message.innerHTML = '';
                        }, 3000);
                    },
                    error: function(xhr, status, error) {
                        message.innerHTML = `<div class="alert alert-danger mt-2" role="alert">An unexpected error occurred.</div>`;
                        setTimeout(function() {
                            message.innerHTML = '';
                        }, 3000);
                    }
                });
            } else {
                console.error('Quill editor is not initialized.');
            }
        });
    });

    function getDataInvoicesHeader(groupId) {
        $.ajax({
            url: '<?php echo e(route("backend.invoice.header.find")); ?>',
            type: 'POST',
            data: {
                groupId: groupId,
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                let fileContent = response.fileGetInvoice;
                let fileStatus = response.statusInvoice;

                if (!window.quillInvoiceHeader) {
                    window.quillInvoiceHeader = new Quill('.snow-editor-invoice-header', {
                        bounds: '.snow-editor-invoice-header',
                        modules: {
                            formula: true,
                            toolbar: fullToolbar
                        },
                        theme: 'snow'
                    });
                }

                if (window.quillInvoiceHeader) {
                    window.quillInvoiceHeader.clipboard.dangerouslyPasteHTML(fileContent);
                }

                document.getElementById('signatureCheckboxInvoiceHeader').checked = (fileStatus == 1);
            },
            error: function(xhr, status, error) {
                console.error('Error fetching signature data:', status, error);
            }
        });
    }
</script><?php /**PATH C:\Users\<USER>\Desktop\crm-ct\crm-ctt\resources\views/backend/agentgroup/invoiceheader.blade.php ENDPATH**/ ?>