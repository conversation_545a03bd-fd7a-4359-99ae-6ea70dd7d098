<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\B2CAgencyConfig;
use App\Models\AgencyProfile;
use App\Models\ApiKeyList;
use App\Models\ContentSources;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class B2CAgencyConfigController extends Controller
{
  public function index(Request $request, $id)
  {
    $agencyProfile = AgencyProfile::where('group_id', $id)->first();
    $b2cConfigs = collect();

    if ($agencyProfile) {
        $b2cConfigs = B2CAgencyConfig::where('agency_profile_id', $agencyProfile->id)->get();
    }

    $content_sources = ContentSources::where('status', 1)->get(['id','identifier', 'name'])->toArray();

    return view('backend.agencygroup.b2cagencyconfig.form', compact(['id', 'agencyProfile', 'b2cConfigs', 'content_sources']));
  }
  public function store(Request $request)
  {
    try {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'group_id' => 'required|string',
            'market_country' => 'required|string',
            'currency' => 'required|string',
            'status' => 'boolean',
            'b2c_config.0.domain' => 'required|string',
            'b2c_config.0.site_name' => 'required|string',
            'b2c_config.0.platform_name' => 'required|string',
            'b2c_config.0.site_email' => 'required|email',
            'b2c_config.0.market_type' => 'required|string',
            'b2c_config.0.site_phone' => 'nullable|string',
            'b2c_config.0.portal_id' => 'required|string|unique:b2c_agency_configs,portal_id',
            'b2c_config.0.rsource' => 'nullable|string',
            'b2c_config.0.api_keys' => 'required|array',
            'b2c_config.0.api_keys.*.api_key' => 'required|string',
            'b2c_config.0.api_keys.*.client_id' => 'required|string',
            'b2c_config.0.content_sources' => 'nullable|array',
            'b2c_config.0.landing_url' => 'nullable|string',
            'b2c_config.0.gds_book' => 'boolean',
            'b2c_config.0.active' => 'boolean',
        ]);

        // Check for duplicate portal_id
        $existingPortalId = B2CAgencyConfig::where('portal_id', $request->b2c_config[0]['portal_id'])->first();
        if ($existingPortalId) {
            return redirect()->back()
                ->withErrors(['portal_id' => 'Portal ID already exists. Please use a different Portal ID.'])
                ->withInput();
        }

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        DB::beginTransaction();

        // Find or create Agency Profile (only one per group_id)
        $agencyProfile = AgencyProfile::where('group_id', $request->group_id)->first();

        if (!$agencyProfile) {
            // Create new agency profile only if it doesn't exist
            $agencyProfile = AgencyProfile::create([
                'group_id' => $request->group_id,
                'market_country' => $request->market_country,
                'currency' => $request->currency,
                'status' => $request->status ?? 0,
            ]);
        } else {
            // Update existing agency profile with new values
            $agencyProfile->update([
                'market_country' => $request->market_country,
                'currency' => $request->currency,
                'status' => $request->status ?? 0,
            ]);
        }

        $b2cConfigData = $request->b2c_config[0];

        // Create new B2C Agency Config (can have multiple per agency_profile_id)
        $b2cConfig = B2CAgencyConfig::create([
            'agency_profile_id' => $agencyProfile->id,
            'domain' => $b2cConfigData['domain'],
            'site_name' => $b2cConfigData['site_name'],
            'platform_name' => $b2cConfigData['platform_name'],
            'site_email' => $b2cConfigData['site_email'],
            'market_type' => $b2cConfigData['market_type'],
            'site_phone' => $b2cConfigData['site_phone'] ?? null,
            'portal_id' => $b2cConfigData['portal_id'],
            'rsource' => $b2cConfigData['rsource'] ?? null,
            'content_sources' => isset($b2cConfigData['content_sources']) ? implode(',', $b2cConfigData['content_sources']) : null,
            'landing_url' => $b2cConfigData['landing_url'] ?? null,
            'booking_mode' => $b2cConfigData['gds_book'] ?? 0,
            'active' => $b2cConfigData['active'] ?? 0,
        ]);

        // Create API Keys
        if (isset($b2cConfigData['api_keys']) && is_array($b2cConfigData['api_keys'])) {
            foreach ($b2cConfigData['api_keys'] as $apiKeyData) {
                ApiKeyList::create([
                    'portal_id' => $b2cConfigData['portal_id'],
                    'api_key' => $apiKeyData['api_key'],
                    'client_id' => $apiKeyData['client_id'],
                ]);
            }
        }

        DB::commit();

        return redirect()->back()->with('success', 'B2C Configuration created successfully!');

    } catch (\Exception $e) {
        DB::rollback();
        return redirect()->back()->with('error', 'Error creating configuration: ' . $e->getMessage());
    }
  }

  public function update(Request $request)
  {
    try {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'group_id' => 'required|string',
            'edit_market_country' => 'required|string',
            'edit_currency' => 'required|string',
            'edit_status' => 'boolean',
            'b2c_config.0.domain' => 'required|string',
            'b2c_config.0.site_name' => 'required|string',
            'b2c_config.0.platform_name' => 'required|string',
            'b2c_config.0.site_email' => 'required|email',
            'b2c_config.0.market_type' => 'required|string',
            'b2c_config.0.site_phone' => 'nullable|string',
            'b2c_config.0.portal_id' => 'required|string',
            'b2c_config.0.rsource' => 'nullable|string',
            'b2c_config.0.api_keys' => 'required|array',
            'b2c_config.0.api_keys.*.api_key' => 'required|string',
            'b2c_config.0.api_keys.*.client_id' => 'required|string',
            'b2c_config.0.content_sources' => 'nullable|array',
            'b2c_config.0.landing_url' => 'nullable|string',
            'b2c_config.0.gds_book' => 'boolean',
            'b2c_config.0.active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Find and update Agency Profile
        $agencyProfile = AgencyProfile::where('group_id', $request->group_id)->first();

        if (!$agencyProfile) {
            throw new \Exception('Agency Profile not found');
        }

        // Find existing B2C Agency Config
        $b2cConfig = B2CAgencyConfig::where('agency_profile_id', $agencyProfile->id)->first();

        if (!$b2cConfig) {
            throw new \Exception('B2C Configuration not found');
        }

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        DB::beginTransaction();

        $agencyProfile->update([
            'market_country' => $request->edit_market_country,
            'currency' => $request->edit_currency,
            'status' => $request->edit_status ?? 0,
        ]);

        $b2cConfigData = $request->b2c_config[0];

        // Find and update B2C Agency Config
        $b2cConfig = B2CAgencyConfig::where('agency_profile_id', $agencyProfile->id)->first();

        if (!$b2cConfig) {
            throw new \Exception('B2C Configuration not found');
        }

        $b2cConfig->update([
            'domain' => $b2cConfigData['domain'],
            'site_name' => $b2cConfigData['site_name'],
            'platform_name' => $b2cConfigData['platform_name'],
            'site_email' => $b2cConfigData['site_email'],
            'market_type' => $b2cConfigData['market_type'],
            'site_phone' => $b2cConfigData['site_phone'] ?? null,
            'portal_id' => $b2cConfigData['portal_id'],
            'rsource' => $b2cConfigData['rsource'] ?? null,
            'content_sources' => isset($b2cConfigData['content_sources']) ? implode(',', $b2cConfigData['content_sources']) : null,
            'landing_url' => $b2cConfigData['landing_url'] ?? null,
            'booking_mode' => $b2cConfigData['gds_book'] ?? 0,
            'active' => $b2cConfigData['active'] ?? 0,
        ]);

        // Update existing API keys or create new ones
        if (isset($b2cConfigData['api_keys']) && is_array($b2cConfigData['api_keys'])) {
            $existingApiKeys = ApiKeyList::where('portal_id', $b2cConfigData['portal_id'])->get();
            $existingApiKeyIds = [];

            foreach ($b2cConfigData['api_keys'] as $index => $apiKeyData) {
                if ($index < $existingApiKeys->count()) {
                    // Update existing API key
                    $existingApiKey = $existingApiKeys[$index];
                    $existingApiKey->update([
                        'api_key' => $apiKeyData['api_key'],
                        'client_id' => $apiKeyData['client_id'],
                    ]);
                    $existingApiKeyIds[] = $existingApiKey->id;
                } else {
                    // Create new API key
                    $newApiKey = ApiKeyList::create([
                        'portal_id' => $b2cConfigData['portal_id'],
                        'api_key' => $apiKeyData['api_key'],
                        'client_id' => $apiKeyData['client_id'],
                    ]);
                    $existingApiKeyIds[] = $newApiKey->id;
                }
            }

            // Delete any remaining API keys that weren't updated
            ApiKeyList::where('portal_id', $b2cConfigData['portal_id'])
                     ->whereNotIn('id', $existingApiKeyIds)
                     ->delete();
        }

        DB::commit();

        return redirect()->back()->with('success', 'B2C Configuration updated successfully!');

    } catch (\Exception $e) {
        DB::rollback();
        return redirect()->back()->with('error', 'Error updating configuration: ' . $e->getMessage());
    }
  }

  public function getApiKeys($portalId)
  {
    try {
        $apiKeys = ApiKeyList::where('portal_id', $portalId)->get(['api_key', 'client_id']);
        return response()->json(['apiKeys' => $apiKeys]);
    } catch (\Exception $e) {
        return response()->json(['error' => 'Failed to fetch API keys'], 500);
    }
  }


}
