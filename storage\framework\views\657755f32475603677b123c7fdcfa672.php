<!-- Modal -->
<div class="modal fade" id="backDropModalInvoice" data-bs-backdrop="static" tabindex="-1">
    <div class="modal-dialog">
        <form class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="backDropModalTitle">Email Header Edit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="message-invoice"></div>
                <div class="row fw-bold">
                    <input type="hidden" id="group-id-template-invoice">
                    <div class="col-md-4">
                        <p class="mb-2">Group Name:</p>
                    </div>
                    <div class="col-md-8"><span id="name-in-template-invoice"></span></div>
                    
                </div>
                <div class="row g-2 mt-3">
                    <div id="agent-note">
                        <div class="snow-editor-invoice"></div>
                    </div>
                    <div class="d-none">
                        <input type="checkbox" class="form-check-input" name="signatureCheckboxInvoice" id="signatureCheckboxInvoice"><label for="signatureCheckboxInvoice"><span style="margin-left: 3px">Active</span></label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" id="save-button-invoice" class="btn btn-primary">Save</button>
            </div>
        </form>
    </div>
</div>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        // Initialize Quill editor
        window.quillInvoice = new Quill('.snow-editor-invoice', {
            bounds: '.snow-editor-invoice',
            modules: {
                formula: true,
                toolbar: fullToolbar
            },
            theme: 'snow'
        });

        // Set image dimensions when text changes
        window.quillInvoice.on('text-change', function(delta, oldDelta, source) {
            if (source === 'user') {
                const images = document.querySelectorAll('.snow-editor-invoice img');
                images.forEach(img => {
                    img.style.width = '178px';
                });
            }
        });

        // Handle save button click
        $('#save-button-invoice').click(function() {
            if (window.quillInvoice) {
                let message = document.getElementById('message-invoice');
                let groupId = document.getElementById('group-id-template-invoice').value;
                let sig = document.getElementById('signatureCheckboxInvoice').checked;
                let editorContent = window.quillInvoice.root.innerHTML;
                editorContent = editorContent.replace(/<\s*(br|hr)\s*\/?>/gi, '');

                // Remove empty tags (<div></div>, <span></span>)
                editorContent = editorContent.replace(/<[^\/>]+>\s*<\/[^>]+>/g, '');
                editorContent = editorContent.replace(/<img\s+([^>]*?)>/g, function(match, p1) {
                    return `<img ${p1} width="178">`;
                });

                // Make AJAX request
                $.ajax({
                    url: '<?php echo e(route("backend.invoice.signaturestore")); ?>',
                    type: 'POST',
                    data: {
                        content: editorContent,
                        groupId: groupId,
                        status: sig,
                        _token: '<?php echo e(csrf_token()); ?>'
                    },
                    success: function(response) {
                        let alertDiv = `<div class="alert alert-success mt-2" role="alert">${response.message}</div>`;
                        if (response.status === 'success') {
                            message.innerHTML = alertDiv;
                        } else {
                            message.innerHTML = `<div class="alert alert-danger mt-2" role="alert">${response.message}</div>`;
                        }
                        setTimeout(function() {
                            message.innerHTML = '';
                        }, 3000);
                    },
                    error: function(xhr, status, error) {
                        message.innerHTML = `<div class="alert alert-danger mt-2" role="alert">An unexpected error occurred.</div>`;
                        setTimeout(function() {
                            message.innerHTML = '';
                        }, 3000);
                    }
                });
            } else {
                console.error('Quill editor is not initialized.');
            }
        });
    });

    function getSignatureDataInvoice(groupId) {
        $.ajax({
            url: '<?php echo e(route("backend.invoice.signature.find")); ?>',
            type: 'POST',
            data: {
                groupId: groupId,
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                let fileContent = response.fileGetInvoice;
                let fileStatus = response.statusInvoice;

                if (!window.quillInvoice) {
                    window.quillInvoice = new Quill('.snow-editor-invoice', {
                        bounds: '.snow-editor-invoice',
                        modules: {
                            formula: true,
                            toolbar: fullToolbar
                        },
                        theme: 'snow'
                    });
                }

                if (window.quillInvoice) {
                    window.quillInvoice.clipboard.dangerouslyPasteHTML(fileContent);
                }

                document.getElementById('signatureCheckboxInvoice').checked = (fileStatus == 1);
            },
            error: function(xhr, status, error) {
                console.error('Error fetching signature data:', status, error);
            }
        });
    }
</script><?php /**PATH C:\Users\<USER>\Desktop\crm-ct\crm-ctt\resources\views/backend/agentgroup/invoicesignature.blade.php ENDPATH**/ ?>