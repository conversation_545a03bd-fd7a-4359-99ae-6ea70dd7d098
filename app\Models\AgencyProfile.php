<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class AgencyProfile extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'agency_profiles';

    protected $fillable = [
        'group_id',
        'market_country',
        'currency',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];
}
