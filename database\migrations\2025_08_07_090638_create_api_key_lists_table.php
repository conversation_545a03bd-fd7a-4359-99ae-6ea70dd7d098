<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('api_key_lists', function (Blueprint $table) {
            $table->id();

            // portal_id referencing b2c_agency_configs.portal_id
            $table->string('portal_id');

            $table->string('api_key');
            $table->string('client_id');

            $table->timestamps();   
            $table->softDeletes();    

            // Add index to the portal_id column for better performance
            $table->index('portal_id');

            // Correct foreign key definition
            $table->foreign('portal_id')
                  ->references('portal_id')
                  ->on('b2c_agency_configs')
                  ->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::table('api_key_lists', function (Blueprint $table) {
            $table->dropForeign(['portal_id']);
        });
        
        Schema::dropIfExists('api_key_lists');
    }
};