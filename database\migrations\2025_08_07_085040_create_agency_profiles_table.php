<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('agency_profiles', function (Blueprint $table) {
            $table->id(); 
            $table->string('group_id'); 
            $table->string('market_country', 2); 
            $table->string('currency', 3);
            $table->boolean('status')->default(true); 
            $table->timestamps();
            $table->softDeletes(); 
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('agency_profiles');
    }
};
